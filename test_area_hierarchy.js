/**
 * File test để kiểm tra hệ thống quản lý khu vực địa bàn với cấu trúc phân cấp
 * 
 * <PERSON><PERSON><PERSON> tính năng được test:
 * 1. <PERSON><PERSON><PERSON> khu vực lớn (level 1)
 * 2. T<PERSON><PERSON> tổ dân phố (level 2) thuộc khu vực lớn
 * 3. Validation cấu trúc phân cấp
 * 4. Populate thông tin areas trong User
 * 5. Filter areas theo level và parent
 */

const mongoose = require('mongoose');
const Area = require('./lib/models/area');
const User = require('./lib/models/user');

// Test data
const testData = {
  // Khu vực lớn (level 1)
  parentArea: {
    name: 'Khu vực A',
    description: 'Khu vực lớn A',
    level: 1,
    boundaries: [
      { lat: 10.123, lng: 106.456 },
      { lat: 10.124, lng: 106.457 }
    ],
    routes: ['<PERSON><PERSON><PERSON><PERSON>ễ<PERSON> Văn A', 'Đ<PERSON>ờng Trần Văn B'],
    familyCount: 100,
    populationCount: 400,
    area: 5000
  },
  
  // Tổ dân phố (level 2)
  childAreas: [
    {
      name: 'Tổ dân phố 1',
      description: 'Tổ dân phố 1 thuộc khu vực A',
      level: 2,
      routes: ['Đường Nguyễn Văn A'],
      familyCount: 30,
      populationCount: 120,
      area: 1500
    },
    {
      name: 'Tổ dân phố 2', 
      description: 'Tổ dân phố 2 thuộc khu vực A',
      level: 2,
      routes: ['Đường Trần Văn B'],
      familyCount: 25,
      populationCount: 100,
      area: 1200
    }
  ],
  
  // User test data
  testUser: {
    username: 'test_user_001',
    password: 'test123456',
    name: 'Nguyễn Văn Test',
    idNumber: '123456',
    email: '<EMAIL>',
    apps: ['ioc']
  }
};

/**
 * Test tạo khu vực lớn (level 1)
 */
async function testCreateParentArea() {
  console.log('\n=== Test tạo khu vực lớn (level 1) ===');
  
  try {
    const parentArea = new Area(testData.parentArea);
    const savedParentArea = await parentArea.save();
    
    console.log('✅ Tạo khu vực lớn thành công:', savedParentArea.name);
    console.log('   - ID:', savedParentArea._id);
    console.log('   - Level:', savedParentArea.level);
    console.log('   - Parent:', savedParentArea.parent);
    
    return savedParentArea;
  } catch (error) {
    console.log('❌ Lỗi khi tạo khu vực lớn:', error.message);
    throw error;
  }
}

/**
 * Test tạo tổ dân phố (level 2)
 */
async function testCreateChildAreas(parentAreaId) {
  console.log('\n=== Test tạo tổ dân phố (level 2) ===');
  
  const createdChildAreas = [];
  
  for (const childAreaData of testData.childAreas) {
    try {
      const childArea = new Area({
        ...childAreaData,
        parent: parentAreaId,
        parentPath: [parentAreaId]
      });
      
      const savedChildArea = await childArea.save();
      createdChildAreas.push(savedChildArea);
      
      console.log('✅ Tạo tổ dân phố thành công:', savedChildArea.name);
      console.log('   - ID:', savedChildArea._id);
      console.log('   - Level:', savedChildArea.level);
      console.log('   - Parent:', savedChildArea.parent);
      console.log('   - ParentPath:', savedChildArea.parentPath);
      
    } catch (error) {
      console.log('❌ Lỗi khi tạo tổ dân phố:', error.message);
      throw error;
    }
  }
  
  return createdChildAreas;
}

/**
 * Test validation cấu trúc phân cấp
 */
async function testValidation() {
  console.log('\n=== Test validation cấu trúc phân cấp ===');
  
  // Test 1: Tạo khu vực lớn với parent (should fail)
  try {
    const invalidParentArea = new Area({
      name: 'Khu vực không hợp lệ',
      level: 1,
      parent: new mongoose.Types.ObjectId()
    });
    
    await invalidParentArea.save();
    console.log('❌ Validation thất bại: Khu vực lớn không được có parent');
  } catch (error) {
    console.log('✅ Validation thành công: Khu vực lớn không được có parent');
  }
  
  // Test 2: Tạo tổ dân phố không có parent (should fail)
  try {
    const invalidChildArea = new Area({
      name: 'Tổ dân phố không hợp lệ',
      level: 2
    });
    
    await invalidChildArea.save();
    console.log('❌ Validation thất bại: Tổ dân phố phải có parent');
  } catch (error) {
    console.log('✅ Validation thành công: Tổ dân phố phải có parent');
  }
}

/**
 * Test populate areas trong User
 */
async function testUserAreasPopulate(areaIds) {
  console.log('\n=== Test populate areas trong User ===');
  
  try {
    // Tạo user test với areas
    const testUser = new User({
      ...testData.testUser,
      areas: areaIds
    });
    
    const savedUser = await testUser.save();
    console.log('✅ Tạo user thành công với areas:', savedUser.areas);
    
    // Test populate areas
    const populatedUser = await User.findById(savedUser._id)
      .populate({
        path: 'areas',
        select: 'name level parent parentPath',
        populate: {
          path: 'parent',
          select: 'name'
        }
      })
      .lean();
    
    console.log('✅ Populate areas thành công:');
    populatedUser.areas.forEach(area => {
      console.log(`   - ${area.name} (Level ${area.level})`);
      if (area.parent) {
        console.log(`     Parent: ${area.parent.name}`);
      }
    });
    
    return savedUser;
  } catch (error) {
    console.log('❌ Lỗi khi test user areas:', error.message);
    throw error;
  }
}

/**
 * Test filter areas theo level và parent
 */
async function testAreaFiltering(parentAreaId) {
  console.log('\n=== Test filter areas theo level và parent ===');
  
  try {
    // Test filter theo level 1 (khu vực lớn)
    const parentAreas = await Area.find({ level: 1, status: 1 })
      .populate('parent', 'name level')
      .lean();
    
    console.log('✅ Filter level 1 (khu vực lớn):');
    parentAreas.forEach(area => {
      console.log(`   - ${area.name}`);
    });
    
    // Test filter theo level 2 (tổ dân phố)
    const childAreas = await Area.find({ level: 2, status: 1 })
      .populate('parent', 'name level')
      .lean();
    
    console.log('✅ Filter level 2 (tổ dân phố):');
    childAreas.forEach(area => {
      console.log(`   - ${area.name} (Parent: ${area.parent?.name})`);
    });
    
    // Test filter theo parent cụ thể
    const areasOfParent = await Area.find({ parent: parentAreaId, status: 1 })
      .populate('parent', 'name level')
      .lean();
    
    console.log('✅ Filter theo parent cụ thể:');
    areasOfParent.forEach(area => {
      console.log(`   - ${area.name}`);
    });
    
  } catch (error) {
    console.log('❌ Lỗi khi test filtering:', error.message);
    throw error;
  }
}

/**
 * Cleanup test data
 */
async function cleanup() {
  console.log('\n=== Cleanup test data ===');
  
  try {
    await Area.deleteMany({ name: { $regex: /Test|Khu vực A|Tổ dân phố/ } });
    await User.deleteMany({ username: 'test_user_001' });
    console.log('✅ Cleanup thành công');
  } catch (error) {
    console.log('❌ Lỗi khi cleanup:', error.message);
  }
}

/**
 * Chạy tất cả tests
 */
async function runAllTests() {
  console.log('🚀 Bắt đầu test hệ thống quản lý khu vực địa bàn phân cấp');
  
  try {
    // Cleanup trước khi test
    await cleanup();
    
    // Test tạo khu vực lớn
    const parentArea = await testCreateParentArea();
    
    // Test tạo tổ dân phố
    const childAreas = await testCreateChildAreas(parentArea._id);
    
    // Test validation
    await testValidation();
    
    // Test user areas populate
    const allAreaIds = [parentArea._id, ...childAreas.map(area => area._id)];
    await testUserAreasPopulate(allAreaIds);
    
    // Test filtering
    await testAreaFiltering(parentArea._id);
    
    console.log('\n🎉 Tất cả tests đã hoàn thành thành công!');
    
  } catch (error) {
    console.log('\n💥 Test thất bại:', error.message);
  } finally {
    // Cleanup sau khi test
    await cleanup();
  }
}

// Export để có thể chạy từ command line
module.exports = {
  runAllTests,
  testCreateParentArea,
  testCreateChildAreas,
  testValidation,
  testUserAreasPopulate,
  testAreaFiltering,
  cleanup
};

// Chạy test nếu file được execute trực tiếp
if (require.main === module) {
  runAllTests().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

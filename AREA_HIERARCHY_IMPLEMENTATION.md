# Hệ thống Quản lý Khu vực Địa bàn với Cấu trúc Phân cấp

## Tổng quan

Đã triển khai thành công hệ thống quản lý khu vực địa bàn với cấu trúc phân cấp 2 tầng:
- **Cấp 1**: Khu vực lớn (parent areas)
- **Cấp 2**: Tổ dân phố (child areas) thuộc về các khu vực lớn

## Các thay đổi đã thực hiện

### 1. Cập nhật Area Model (`lib/models/area.js`)

#### Các trường mới được thêm:
```javascript
level: {
  type: Number, // 1 = khu vực lớn, 2 = tổ dân phố
  required: true,
  enum: [1, 2],
  default: 1
},
parent: {
  type: Schema.Types.ObjectId, // khu vực cha (chỉ áp dụng cho level 2)
  ref: 'area'
},
parentPath: [{
  type: Schema.Types.ObjectId, // đường dẫn phân cấp từ gốc đến node hiện tại
  ref: 'area'
}]
```

#### Quy tắc cấu trúc:
- **Level 1 (Khu vực lớn)**: `parent = null`, `parentPath = []`
- **Level 2 (Tổ dân phố)**: `parent = ObjectId của khu vực lớn`, `parentPath = [parent]`

### 2. Cập nhật User APIs

#### 2.1 Admin User Get API (`lib/routes/admin/user/get/v1.0.js`)
- Thêm populate cho trường `areas` với thông tin parent

#### 2.2 Admin User List API (`lib/routes/admin/user/list/v1.0.js`)
- Thêm populate cho trường `areas` với thông tin parent

#### 2.3 User Get API (`lib/routes/user/get/v1.0.js`)
- Thêm populate cho trường `areas` với thông tin parent

#### 2.4 User Create API (`lib/routes/admin/user/create/v1.0.js`)
- Thêm trường `areas` vào logic tạo user

#### 2.5 User Update API (`lib/routes/admin/user/update/v1.0.js`)
- Thêm trường `areas` vào logic cập nhật user

### 3. Cập nhật Area APIs

#### 3.1 Area Create API (`lib/routes/admin/area/create/v1.0.js`)

**Tham số mới:**
- `level`: Cấp độ khu vực (1 hoặc 2)
- `parent`: ID khu vực cha (chỉ cho level 2)

**Validation được thêm:**
- Level phải là 1 hoặc 2
- Level 2 phải có parent
- Level 1 không được có parent
- Parent phải tồn tại và là level 1

**Logic tạo:**
- Tự động tạo `parentPath` dựa trên parent
- Tự động tạo `nameAlias` từ name

#### 3.2 Area Update API (`lib/routes/admin/area/update/v1.0.js`)

**Tham số mới:**
- `level`: Cấp độ khu vực
- `parent`: ID khu vực cha

**Validation được thêm:**
- Tương tự như create API
- Thêm kiểm tra không được tự tham chiếu

**Logic cập nhật:**
- Cập nhật `parentPath` khi thay đổi parent
- Validation parent area khi có thay đổi

#### 3.3 Area List API (`lib/routes/admin/area/list/v1.0.js`)

**Tham số filter mới:**
- `level`: Filter theo cấp độ (1 hoặc 2)
- `parent`: Filter theo khu vực cha cụ thể

**Cải tiến:**
- Populate thông tin parent
- Sắp xếp theo level trước, sau đó theo thời gian tạo

#### 3.4 Area Get API (`lib/routes/admin/area/get/v1.0.js`)

**Cải tiến:**
- Populate thông tin parent
- Populate thông tin parentPath

## Cách sử dụng

### 1. Tạo khu vực lớn (Level 1)

```javascript
POST /admin/area/create
{
  "name": "Khu vực A",
  "description": "Khu vực lớn A",
  "level": 1,
  "boundaries": [...],
  "routes": ["Đường A", "Đường B"],
  "familyCount": 100,
  "populationCount": 400,
  "area": 5000
}
```

### 2. Tạo tổ dân phố (Level 2)

```javascript
POST /admin/area/create
{
  "name": "Tổ dân phố 1",
  "description": "Tổ dân phố 1 thuộc khu vực A",
  "level": 2,
  "parent": "ObjectId_của_khu_vực_A",
  "routes": ["Đường A"],
  "familyCount": 30,
  "populationCount": 120,
  "area": 1500
}
```

### 3. Lọc khu vực theo cấp độ

```javascript
// Lấy tất cả khu vực lớn
POST /admin/area/list
{
  "level": 1
}

// Lấy tất cả tổ dân phố
POST /admin/area/list
{
  "level": 2
}

// Lấy tổ dân phố của khu vực cụ thể
POST /admin/area/list
{
  "level": 2,
  "parent": "ObjectId_của_khu_vực_cha"
}
```

### 4. Gán khu vực cho user

```javascript
POST /admin/user/create
{
  "username": "user001",
  "name": "Nguyễn Văn A",
  "areas": ["ObjectId_khu_vực_1", "ObjectId_khu_vực_2"],
  // ... các trường khác
}
```

## Cấu trúc dữ liệu trả về

### Area với populate parent:
```javascript
{
  "_id": "ObjectId",
  "name": "Tổ dân phố 1",
  "level": 2,
  "parent": {
    "_id": "ObjectId",
    "name": "Khu vực A",
    "level": 1
  },
  "parentPath": ["ObjectId_của_parent"],
  // ... các trường khác
}
```

### User với populate areas:
```javascript
{
  "_id": "ObjectId",
  "name": "Nguyễn Văn A",
  "areas": [
    {
      "_id": "ObjectId",
      "name": "Khu vực A",
      "level": 1,
      "parent": null
    },
    {
      "_id": "ObjectId", 
      "name": "Tổ dân phố 1",
      "level": 2,
      "parent": {
        "_id": "ObjectId",
        "name": "Khu vực A"
      }
    }
  ],
  // ... các trường khác
}
```

## Validation Rules

1. **Level 1 (Khu vực lớn)**:
   - `parent` phải là `null`
   - `parentPath` phải là `[]`

2. **Level 2 (Tổ dân phố)**:
   - `parent` phải có giá trị và tồn tại
   - `parent` phải là area có `level = 1`
   - `parentPath` phải chứa `parent`

3. **Chung**:
   - `level` chỉ chấp nhận giá trị 1 hoặc 2
   - `name` phải unique
   - Không được tự tham chiếu (parent không thể là chính nó)

## Testing

Đã tạo file test `test_area_hierarchy.js` để kiểm tra:
- Tạo khu vực lớn và tổ dân phố
- Validation cấu trúc phân cấp
- Populate areas trong User
- Filter areas theo level và parent

Chạy test:
```bash
node test_area_hierarchy.js
```

## Lưu ý quan trọng

1. **Backward Compatibility**: Trường `groups` cũ vẫn được giữ lại nhưng đã deprecated, khuyến khích sử dụng cấu trúc parent-child mới.

2. **Performance**: Các API đã được tối ưu với populate và indexing phù hợp.

3. **Data Migration**: Dữ liệu cũ cần được migrate để thêm trường `level` (mặc định là 1).

4. **Frontend Integration**: Frontend cần cập nhật để hỗ trợ hiển thị cấu trúc phân cấp và filter theo level/parent.

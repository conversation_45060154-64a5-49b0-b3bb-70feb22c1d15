# Hướng dẫn Script Khởi tạo JobType

## Tổng quan

Script `initJobTypes.js` đã được viết lại hoàn toàn để tuân thủ các yêu cầu kỹ thuật và tương thích với cấu trúc database hiện tại.

## Các cải tiến chính

### 1. **<PERSON>ân thủ chính xác model JobType**
- Sử dụng đúng cấu trúc schema của `lib/models/jobType.js`
- <PERSON><PERSON><PERSON> bảo tất cả trường bắt buộc được điền đầy đủ
- Sử dụng đúng kiểu dữ liệu (String, ObjectId, Number)

### 2. **Sử dụng function changeAlias**
- Import và sử dụng `change_alias` từ `lib/util/tool.js`
- Tự động convert `nameA<PERSON><PERSON>` từ trường `name`
- Loại bỏ hardcode nameAlias như trước đây

### 3. **Sử dụng dữ liệu Unit có sẵn**
- Không tạo mới Unit, chỉ query và sử dụng Unit hiện có
- Tìm Unit bằng tên chính xác
- Sử dụng `_id` của Unit để liên kết với JobType

### 4. **Xử lý lỗi và logging**
- Logging bằng tiếng Việt, dễ hiểu
- Xử lý lỗi chi tiết cho từng bước
- Thống kê kết quả cuối cùng

### 5. **Tối ưu hiệu suất**
- Sử dụng Map để cache Unit data
- Kiểm tra duplicate trước khi tạo
- Batch processing hiệu quả

## Cấu trúc Script

### Files được tạo/cập nhật:

1. **`scripts/initJobTypes.js`** - Script chính
2. **`scripts/testConnection.js`** - Script test kết nối
3. **`lib/models/unit.js`** - Sửa lỗi thiếu import mongoose

### Functions chính:

- `connectAndInit()` - Kết nối MongoDB và khởi chạy
- `findExistingUnits()` - Tìm Unit có sẵn trong database
- `checkJobTypeExists()` - Kiểm tra JobType đã tồn tại
- `createJobType()` - Tạo JobType mới với nameAlias tự động
- `displayFinalStatistics()` - Hiển thị thống kê chi tiết

## Cách sử dụng

### Bước 1: Test kết nối
```bash
node scripts/testConnection.js
```

**Output mẫu:**
```
[INFO] Đang kết nối MongoDB...
[SUCCESS] Kết nối MongoDB thành công

Test function change_alias:
Input: "Phân công lịch trực"
Output: phanconglichtruc

Tìm kiếm Units trong database:
Tìm thấy 5 units:
- Tổ Tổng hợp (ID: 60f1b2c3d4e5f6789abcdef0)
- Tổ An ninh (ID: 60f1b2c3d4e5f6789abcdef1)
...
```

### Bước 2: Khởi tạo JobTypes
```bash
node scripts/initJobTypes.js
```

**Output mẫu:**
```
[INFO] Đang kết nối MongoDB...
[SUCCESS] Kết nối MongoDB thành công
[INFO] Bắt đầu khởi tạo dữ liệu JobType...
[INFO] Đang tìm kiếm các đơn vị có sẵn...
[INFO] Tìm thấy 5 đơn vị trong database

[INFO] Xử lý đơn vị: Tổ Tổng hợp
[INFO] Tìm thấy đơn vị: Tổ Tổng hợp (ID: 60f1b2c3d4e5f6789abcdef0)
[SUCCESS]   Tạo thành công JobType: "Phân công lịch trực" (Alias: phanconglichtruc)
[SUCCESS]   Tạo thành công JobType: "Nhắc lịch họp" (Alias: nhaclichhop)
...

=== THỐNG KÊ KẾT QUẢ ===
[INFO] Tổng số JobType trong database: 15
[INFO] JobType được tạo mới: 15
[INFO] JobType đã tồn tại (bỏ qua): 0
[INFO] Số lỗi xảy ra: 0

Thống kê JobType theo đơn vị:
- Tổ Tổng hợp: 5 loại công việc
- Tổ Cảnh sát trật tự: 4 loại công việc
- Tổ Cảnh sát khu vực: 5 loại công việc
- Tổ Cảnh sát phòng chống tội phạm: 1 loại công việc
- Tổ An ninh: 1 loại công việc

[SUCCESS] Khởi tạo dữ liệu JobType hoàn thành!
```

## Dữ liệu được tạo

### 15 JobTypes cho 5 tổ công tác:

**Tổ Tổng hợp (5 loại):**
- Phân công lịch trực → `phanconglichtruc`
- Nhắc lịch họp → `nhaclichhop`
- Thống kê công văn đến → `thongkecongvanden`
- Phân loại công văn → `phanloaicongvan`
- Giám sát tiến độ công văn → `giamsattienducongvan`

**Tổ An ninh (1 loại):**
- Bảo vệ đoàn công tác → `baovedoncongtac`

**Tổ Cảnh sát trật tự (4 loại):**
- Tuần tra kiểm soát TTATGT → `tuantrikiemsoatttatgt`
- Cấp đăng ký biển số xe → `capdangkybiensoze`
- Thu hồi đăng ký biển số xe → `thuhoidangkybiensoze`
- Tai nạn giao thông → `tainangiaothong`

**Tổ Cảnh sát phòng chống tội phạm (1 loại):**
- Đấu tranh phòng chống tội phạm → `dautranhphongchongtoipham`

**Tổ Cảnh sát khu vực (5 loại):**
- Cấp Căn cước công dân → `capcancuoccongdan`
- Định danh điện tử → `dinhdanhdientu`
- Dịch vụ công trực tuyến → `dichvucongtructuyen`
- Mâu thuẫn nội bộ nhân dân → `mauthuannoibonhandan`
- Cháy nổ → `chayno`

## Xử lý lỗi

### Các trường hợp lỗi được xử lý:

1. **Lỗi kết nối MongoDB**: Script dừng với thông báo rõ ràng
2. **Unit không tồn tại**: Bỏ qua và thông báo warning
3. **JobType đã tồn tại**: Bỏ qua và đếm vào thống kê
4. **Lỗi tạo JobType**: Log lỗi chi tiết và tiếp tục
5. **Lỗi validation**: Hiển thị thông tin lỗi cụ thể

### Logging levels:
- `[INFO]` - Thông tin chung
- `[SUCCESS]` - Thao tác thành công
- `[WARN]` - Cảnh báo (không dừng script)
- `[ERROR]` - Lỗi nghiêm trọng

## Tính năng an toàn

1. **Không ghi đè dữ liệu**: Kiểm tra duplicate trước khi tạo
2. **Rollback không cần thiết**: Chỉ tạo mới, không sửa/xóa
3. **Validation đầy đủ**: Kiểm tra dữ liệu trước khi lưu
4. **Graceful exit**: Đóng kết nối đúng cách khi kết thúc

## Tương thích

- ✅ Tương thích với cấu trúc database hiện tại
- ✅ Không ảnh hưởng đến dữ liệu có sẵn
- ✅ Sử dụng connection pool hiện có
- ✅ Tuân thủ coding convention của project
- ✅ Hỗ trợ chạy nhiều lần an toàn

Script đã sẵn sàng để sử dụng trong môi trường production!

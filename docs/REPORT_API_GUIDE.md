# Hướng dẫn sử dụng API Báo cáo Công việc

## Tổng quan

Hệ thống API báo cáo công việc được thiết kế để hỗ trợ các tổ công tác trong Công an phường Hồng Bàng báo cáo và thống kê các hoạt động hàng ngày.

## Các loại báo cáo

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON> (Quick Report)
- **M<PERSON><PERSON> đích**: <PERSON><PERSON>o cáo số lượng sự việc theo từng loại công việc
- **Đặc điểm**: <PERSON><PERSON> thể báo cáo nhiều vụ cùng lúc, chỉ quan tâm đến số lượng
- **Sử dụng**: Th<PERSON><PERSON> hợp cho các công việc thường xuyên, lặp lại

### 2. <PERSON><PERSON><PERSON> c<PERSON><PERSON> chi tiết (Detailed Report)
- **<PERSON><PERSON><PERSON> đ<PERSON>ch**: <PERSON><PERSON><PERSON> cáo trạng thái xử lý từng vụ việc cụ thể
- **Đặc điểm**: Chỉ báo cáo 1 vụ/lần, theo dõi trạng thái xử lý
- **Sử dụng**: Thích hợp cho các vụ việc quan trọng cần theo dõi chi tiết

## Danh sách API Endpoints

### 1. Tạo báo cáo nhanh
```
POST /api/v1.0/report/create-quick
```

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Body:**
```json
{
  "jobTypeId": "ObjectId của loại công việc",
  "areaId": "ObjectId của khu vực (tùy chọn)",
  "reportDate": "2024-01-15",
  "quantity": 5,
  "note": "Ghi chú ngắn gọn (tùy chọn)"
}
```

**Response thành công:**
```json
{
  "code": 200,
  "message": {
    "head": "Thông báo",
    "body": "Tạo báo cáo nhanh thành công"
  },
  "data": {
    "reportId": "ObjectId",
    "reportDate": "2024-01-15",
    "quantity": 5,
    "jobType": "Tên loại công việc",
    "area": "Tên khu vực"
  }
}
```

### 2. Tạo báo cáo chi tiết
```
POST /api/v1.0/report/create-detailed
```

**Body:**
```json
{
  "jobTypeId": "ObjectId của loại công việc",
  "areaId": "ObjectId của khu vực (tùy chọn)",
  "reportDate": "2024-01-15",
  "caseCode": "VU001",
  "status": "processing", // hoặc "completed"
  "description": "Mô tả chi tiết vụ việc",
  "startTime": 1705123456789,
  "completedTime": 1705234567890, // bắt buộc nếu status = "completed"
  "result": "Kết quả xử lý" // bắt buộc nếu status = "completed"
}
```

### 3. Cập nhật báo cáo
```
POST /api/v1.0/report/update
```

**Body cho báo cáo nhanh:**
```json
{
  "reportId": "ObjectId của báo cáo",
  "quantity": 10,
  "note": "Ghi chú mới"
}
```

**Body cho báo cáo chi tiết:**
```json
{
  "reportId": "ObjectId của báo cáo",
  "status": "completed",
  "description": "Mô tả cập nhật",
  "completedTime": 1705234567890,
  "result": "Kết quả xử lý cuối cùng"
}
```

### 4. Lấy danh sách báo cáo
```
POST /api/v1.0/report/list
```

**Body:**
```json
{
  "reportType": "quick", // hoặc "detailed" hoặc không có (lấy tất cả)
  "jobTypeId": "ObjectId (tùy chọn)",
  "areaId": "ObjectId (tùy chọn)",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "status": "processing", // chỉ áp dụng cho báo cáo chi tiết
  "page": 1,
  "limit": 20
}
```

### 5. Thống kê báo cáo
```
POST /api/v1.0/report/statistics
```

**Body:**
```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "jobTypeId": "ObjectId (tùy chọn)",
  "areaId": "ObjectId (tùy chọn)",
  "groupBy": "date" // hoặc "jobType", "area", "status"
}
```

### 6. Xóa báo cáo
```
POST /api/v1.0/report/delete
```

**Body:**
```json
{
  "reportId": "ObjectId của báo cáo"
}
```

### 7. Lấy danh sách loại công việc
```
POST /api/v1.0/admin/job-type/list
```

**Body:**
```json
{
  "unitId": "ObjectId của đơn vị (tùy chọn)",
  "search": "từ khóa tìm kiếm (tùy chọn)",
  "page": 1,
  "limit": 50
}
```

## Phân quyền

### Quyền truy cập API báo cáo:
- Tất cả các API báo cáo yêu cầu xác thực bằng JWT token
- Người dùng chỉ có thể tạo/sửa/xóa báo cáo của chính mình
- Quyền báo cáo được kiểm tra theo:
  - **Đơn vị**: Người dùng chỉ có thể báo cáo cho các loại công việc thuộc đơn vị của mình
  - **Khu vực**: Người dùng chỉ có thể báo cáo cho các khu vực được phân công

### Quyền truy cập API quản trị:
- API lấy danh sách loại công việc: Tất cả người dùng đã xác thực
- Các API quản lý loại công việc khác: Cần quyền quản trị tương ứng

## Ví dụ sử dụng

### Ví dụ 1: Báo cáo nhanh số lượt tuần tra
```bash
curl -X POST http://localhost:9654/api/v1.0/report/create-quick \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "jobTypeId": "60f1b2c3d4e5f6789abcdef0",
    "areaId": "60f1b2c3d4e5f6789abcdef1",
    "reportDate": "2024-01-15",
    "quantity": 3,
    "note": "Tuần tra khu vực trung tâm"
  }'
```

### Ví dụ 2: Báo cáo chi tiết vụ tai nạn giao thông
```bash
curl -X POST http://localhost:9654/api/v1.0/report/create-detailed \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "jobTypeId": "60f1b2c3d4e5f6789abcdef2",
    "areaId": "60f1b2c3d4e5f6789abcdef1",
    "reportDate": "2024-01-15",
    "caseCode": "TNGT001",
    "status": "processing",
    "description": "Tai nạn giao thông tại ngã tư Lạch Tray",
    "startTime": 1705123456789
  }'
```

### Ví dụ 3: Thống kê theo ngày
```bash
curl -X POST http://localhost:9654/api/v1.0/report/statistics \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "startDate": "2024-01-01",
    "endDate": "2024-01-31",
    "groupBy": "date"
  }'
```

## Lưu ý quan trọng

1. **Định dạng ngày**: Sử dụng định dạng YYYY-MM-DD
2. **Mã vụ việc**: Mỗi mã vụ việc phải duy nhất trong phạm vi người dùng + loại công việc + khu vực
3. **Trạng thái hoàn thành**: Khi cập nhật trạng thái thành "completed", bắt buộc phải có completedTime và result
4. **Phân trang**: Giới hạn tối đa 100 items/page
5. **Xóa mềm**: Báo cáo bị xóa chỉ được đánh dấu status = 0, không xóa vật lý khỏi database

## Khởi tạo dữ liệu mẫu

### Bước 1: Kiểm tra kết nối và dữ liệu hiện có

```bash
node scripts/testConnection.js
```

Script này sẽ:
- Kiểm tra kết nối MongoDB
- Test function change_alias
- Hiển thị các Unit có sẵn trong database
- Hiển thị JobType hiện có (nếu có)

### Bước 2: Khởi tạo JobTypes

```bash
node scripts/initJobTypes.js
```

Script này sẽ:
- Tìm kiếm 5 đơn vị tổ công tác có sẵn trong database
- Tạo 15 loại công việc tương ứng với yêu cầu của từng tổ
- Sử dụng function change_alias để tự động tạo nameAlias
- Hiển thị thống kê chi tiết kết quả

### Lưu ý quan trọng:
- Script sẽ **không tạo mới** các Unit, chỉ sử dụng Unit có sẵn
- Nếu Unit không tồn tại, script sẽ bỏ qua và thông báo
- JobType trùng lặp sẽ được bỏ qua, không ghi đè
- Script có xử lý lỗi và logging chi tiết

# Hướng dẫn API Quản lý JobType (Loại công việc)

## Tổng quan

Hệ thống API quản lý JobType cung cấp đầy đủ các chức năng CRUD để quản lý các loại công việc trong hệ thống báo cáo của Công an phường Hồng Bàng.

## Danh sách API Endpoints

### 1. L<PERSON>y danh sách JobType
```
POST /api/v1.0/admin/job-type/list
```

**Headers:**
```
Authorization: Bearer <token>
Content-Type: application/json
```

**Body:**
```json
{
  "unitId": "ObjectId của đơn vị (tùy chọn)",
  "search": "từ khóa tìm kiếm (tùy chọn)",
  "page": 1,
  "limit": 50
}
```

**Response:**
```json
{
  "code": 200,
  "message": {
    "head": "Thông báo",
    "body": "<PERSON><PERSON><PERSON> danh sách loại công việc thành công"
  },
  "data": {
    "jobTypes": [
      {
        "id": "ObjectId",
        "name": "Phân công lịch trực",
        "nameAlias": "phanconglichtruc",
        "description": "Phân công lịch trực cho 171 tài khoản cán bộ",
        "unit": {
          "id": "ObjectId",
          "name": "Tổ Tổng hợp",
          "nameAlias": "to-tong-hop"
        },
        "createdAt": 1705123456789,
        "updatedAt": 1705123456789
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 15,
      "itemsPerPage": 50,
      "hasNextPage": false,
      "hasPrevPage": false
    }
  }
}
```

### 2. Lấy thông tin chi tiết JobType
```
POST /api/v1.0/admin/job-type/get
```

**Body:**
```json
{
  "jobTypeId": "ObjectId của JobType"
}
```

**Response:**
```json
{
  "code": 200,
  "message": {
    "head": "Thông báo",
    "body": "Lấy thông tin loại công việc thành công"
  },
  "data": {
    "id": "ObjectId",
    "name": "Phân công lịch trực",
    "nameAlias": "phanconglichtruc",
    "description": "Phân công lịch trực cho 171 tài khoản cán bộ",
    "unit": {
      "id": "ObjectId",
      "name": "Tổ Tổng hợp",
      "nameAlias": "to-tong-hop"
    },
    "createdAt": 1705123456789,
    "updatedAt": 1705123456789
  }
}
```

### 3. Tạo mới JobType
```
POST /api/v1.0/admin/job-type/create
```

**Quyền yêu cầu:** `quan-ly-loai-cong-viec`

**Body:**
```json
{
  "name": "Tên loại công việc",
  "description": "Mô tả chi tiết loại công việc",
  "unitId": "ObjectId của đơn vị"
}
```

**Validation:**
- `name`: 3-100 ký tự, không được trống
- `description`: 10-500 ký tự, không được trống
- `unitId`: Phải tồn tại và đang hoạt động
- Tên không được trùng trong cùng đơn vị

**Response:**
```json
{
  "code": 200,
  "message": {
    "head": "Thông báo",
    "body": "Tạo loại công việc thành công"
  },
  "data": {
    "id": "ObjectId",
    "name": "Tên loại công việc",
    "nameAlias": "tenloaicongviec", // Tự động tạo từ name
    "description": "Mô tả chi tiết loại công việc",
    "unit": {
      "id": "ObjectId",
      "name": "Tên đơn vị",
      "nameAlias": "ten-don-vi"
    },
    "createdAt": 1705123456789,
    "updatedAt": 1705123456789
  }
}
```

### 4. Cập nhật JobType
```
POST /api/v1.0/admin/job-type/update
```

**Quyền yêu cầu:** `quan-ly-loai-cong-viec`

**Body:**
```json
{
  "jobTypeId": "ObjectId của JobType",
  "name": "Tên mới (tùy chọn)",
  "description": "Mô tả mới (tùy chọn)",
  "unitId": "ObjectId đơn vị mới (tùy chọn)"
}
```

**Lưu ý:**
- Có thể cập nhật từng phần hoặc tất cả
- Khi cập nhật `name`, `nameAlias` sẽ tự động được tạo lại
- Có thể chuyển JobType sang đơn vị khác (nếu có quyền)
- Kiểm tra duplicate name trong đơn vị đích

**Response:** Tương tự như API create

### 5. Vô hiệu hóa JobType
```
POST /api/v1.0/admin/job-type/inactive
```

**Quyền yêu cầu:** `quan-ly-loai-cong-viec`

**Body:**
```json
{
  "jobTypeId": "ObjectId của JobType"
}
```

**Response:**
```json
{
  "code": 200,
  "message": {
    "head": "Thông báo",
    "body": "Vô hiệu hóa loại công việc thành công. Lưu ý: Có 5 báo cáo đang sử dụng loại công việc này"
  },
  "data": {
    "id": "ObjectId",
    "name": "Tên JobType",
    "nameAlias": "tenJobType",
    "description": "Mô tả",
    "unit": {
      "id": "ObjectId",
      "name": "Tên đơn vị",
      "nameAlias": "ten-don-vi"
    },
    "status": 0,
    "deletedAt": 1705234567890,
    "affectedReports": 5,
    "createdAt": 1705123456789,
    "updatedAt": 1705234567890
  }
}
```

## Phân quyền

### Quyền truy cập:
- **Xem danh sách và chi tiết**: Tất cả user đã xác thực
- **Tạo/Sửa/Xóa**: Cần quyền `quan-ly-loai-cong-viec`

### Phân quyền theo đơn vị:
- User chỉ có thể xem/quản lý JobType thuộc đơn vị được phân công
- Khi tạo/sửa JobType, phải có quyền trên đơn vị đích
- Khi chuyển JobType sang đơn vị khác, cần quyền trên cả 2 đơn vị

## Tính năng đặc biệt

### 1. Tự động tạo nameAlias
- Sử dụng function `change_alias` từ `lib/util/tool.js`
- Chuyển đổi tiếng Việt có dấu thành không dấu
- Loại bỏ ký tự đặc biệt, chỉ giữ chữ và số

### 2. Soft Delete
- JobType bị xóa không bị xóa vật lý
- Đánh dấu `status = 0` và thêm `deletedAt`
- Kiểm tra số báo cáo đang sử dụng trước khi xóa

### 3. Validation nghiêm ngặt
- Kiểm tra duplicate name trong cùng đơn vị
- Validation độ dài và format dữ liệu
- Kiểm tra quyền truy cập theo đơn vị

## Ví dụ sử dụng

### Tạo JobType mới:
```bash
curl -X POST http://localhost:9654/api/v1.0/admin/job-type/create \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Kiểm tra an ninh",
    "description": "Thực hiện kiểm tra an ninh định kỳ tại các khu vực trọng điểm",
    "unitId": "60f1b2c3d4e5f6789abcdef1"
  }'
```

### Cập nhật JobType:
```bash
curl -X POST http://localhost:9654/api/v1.0/admin/job-type/update \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "jobTypeId": "60f1b2c3d4e5f6789abcdef2",
    "description": "Mô tả đã được cập nhật với thông tin chi tiết hơn"
  }'
```

### Lấy danh sách theo đơn vị:
```bash
curl -X POST http://localhost:9654/api/v1.0/admin/job-type/list \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "unitId": "60f1b2c3d4e5f6789abcdef1",
    "search": "tuần tra",
    "page": 1,
    "limit": 20
  }'
```

## Lưu ý quan trọng

1. **Backup trước khi xóa**: JobType bị xóa sẽ ảnh hưởng đến báo cáo hiện có
2. **Kiểm tra quyền**: Đảm bảo user có quyền trên đơn vị trước khi thao tác
3. **Tên duy nhất**: Mỗi đơn vị chỉ có thể có một JobType với tên cụ thể
4. **Soft delete**: JobType bị xóa vẫn tồn tại trong database để tham chiếu
5. **nameAlias tự động**: Không cần nhập nameAlias, hệ thống tự tạo

Hệ thống API JobType đã sẵn sàng để tích hợp và sử dụng!

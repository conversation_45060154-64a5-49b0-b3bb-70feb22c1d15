/**
 * Script test kết nối database và kiểm tra models
 */

const mongoose = require('mongoose');
const config = require('config');
global.Logger = require('../lib/logger');
global.logger = Logger(`${__dirname}/logs`);

// Import utilities
const { change_alias } = require('../lib/util/tool');

// Kết nối MongoDB
const mongoConfig = config.get('mongo.connections.master');
const mongoUri = `mongodb://${mongoConfig.host}:${mongoConfig.port}/${mongoConfig.database}`;

// Models
const JobTypeModel = require('../lib/models/jobType');
const UnitModel = require('../lib/models/unit');

async function testConnection() {
  try {
    console.log('Đang kết nối MongoDB...');
    await mongoose.connect(mongoUri, mongoConfig.options);
    console.log('Kết nối MongoDB thành công');

    // Test function change_alias
    console.log('\nTest function change_alias:');
    console.log('Input: "Phân công lịch trực"');
    console.log('Output:', change_alias('Phân công lịch trực'));

    // Test tìm Units
    console.log('\nTìm kiếm Units trong database:');
    const units = await UnitModel.find({ status: 1 }).select('_id name nameAlias');
    console.log(`Tìm thấy ${units.length} units:`);
    units.forEach(unit => {
      console.log(`- ${unit.name} (ID: ${unit._id})`);
    });

    // Test tìm JobTypes
    console.log('\nTìm kiếm JobTypes trong database:');
    const jobTypes = await JobTypeModel.find().populate('unit', 'name').limit(5);
    console.log(`Tìm thấy ${jobTypes.length} jobTypes (hiển thị 5 đầu tiên):`);
    jobTypes.forEach(jobType => {
      console.log(`- ${jobType.name} (Unit: ${jobType.unit ? jobType.unit.name : 'N/A'})`);
    });

    console.log('\nTest hoàn thành!');
    process.exit(0);

  } catch (error) {
    console.error('Lỗi:', error.message);
    process.exit(1);
  }
}

testConnection();

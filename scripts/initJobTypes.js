/**
 * Script khởi tạo dữ liệu mẫu cho JobType (Loại công việc)
 * Chạy script này để tạo các loại công việc theo từng tổ công tác
 *
 * Cách chạy: node scripts/initJobTypes.js
 */

const mongoose = require('mongoose');
const config = require('config');
global.Logger = require('../lib/logger');
global.logger = Logger(`${__dirname}/logs`);

// Import utilities
const { change_alias } = require('../lib/util/tool');

// Kết nối MongoDB
const mongoConfig = config.get('mongo.connections.master');
const mongoUri = `mongodb://${mongoConfig.host}:${mongoConfig.port}/${mongoConfig.database}`;

// Models
const JobTypeModel = require('../lib/models/jobType');
const UnitModel = require('../lib/models/unit');

// Logging helper
const log = {
  info: (message) => console.log(`[INFO] ${message}`),
  error: (message) => console.error(`[ERROR] ${message}`),
  success: (message) => console.log(`[SUCCESS] ${message}`),
  warn: (message) => console.warn(`[WARN] ${message}`)
};

// Dữ liệu mẫu cho các loại công việc theo từng tổ
const jobTypesData = [
  // Tổ Tổng hợp
  {
    unitName: 'Tổ Tổng hợp',
    jobTypes: [
      {
        name: 'Phân công lịch trực',
        description: 'Phân công lịch trực cho 171 tài khoản cán bộ'
      },
      {
        name: 'Nhắc lịch họp',
        description: 'Nhắc lịch họp của Chỉ huy Công an phường'
      },
      {
        name: 'Thống kê công văn đến',
        description: 'Thống kê số lượng công văn đến'
      },
      {
        name: 'Phân loại công văn',
        description: 'Phân loại công văn theo lĩnh vực'
      },
      {
        name: 'Giám sát tiến độ công văn',
        description: 'Giám sát tiến độ thực hiện nhiệm vụ của các công văn đã chuyển giao'
      }
    ]
  },

  // Tổ An ninh
  {
    unitName: 'Tổ An ninh',
    jobTypes: [
      {
        name: 'Bảo vệ đoàn công tác',
        description: 'Thống kê số lượt bảo vệ đoàn công tác đi qua địa bàn phường'
      }
    ]
  },

  // Tổ Cảnh sát trật tự
  {
    unitName: 'Tổ Cảnh sát trật tự',
    jobTypes: [
      {
        name: 'Tuần tra kiểm soát TTATGT',
        description: 'Thống kê số lượt tuần tra kiểm soát trật tự an toàn giao thông hàng ngày'
      },
      {
        name: 'Cấp đăng ký biển số xe',
        description: 'Thống kê số lượng cấp đăng ký biển số xe'
      },
      {
        name: 'Thu hồi đăng ký biển số xe',
        description: 'Thống kê số lượng thu hồi đăng ký biển số xe'
      },
      {
        name: 'Tai nạn giao thông',
        description: 'Thống kê số vụ tai nạn giao thông'
      }
    ]
  },

  // Tổ Cảnh sát phòng chống tội phạm
  {
    unitName: 'Tổ Cảnh sát phòng chống tội phạm',
    jobTypes: [
      {
        name: 'Đấu tranh phòng chống tội phạm',
        description: 'Thống kê số vụ việc trong công tác đấu tranh phòng chống tội phạm'
      }
    ]
  },

  // Tổ Cảnh sát khu vực
  {
    unitName: 'Tổ Cảnh sát khu vực',
    jobTypes: [
      {
        name: 'Cấp Căn cước công dân',
        description: 'Thống kê số liệu cấp Căn cước công dân'
      },
      {
        name: 'Định danh điện tử',
        description: 'Thống kê số liệu định danh điện tử'
      },
      {
        name: 'Dịch vụ công trực tuyến',
        description: 'Thống kê số liệu dịch vụ công trực tuyến'
      },
      {
        name: 'Mâu thuẫn nội bộ nhân dân',
        description: 'Thống kê số vụ mâu thuẫn nội bộ nhân dân'
      },
      {
        name: 'Cháy nổ',
        description: 'Thống kê số vụ cháy nổ'
      }
    ]
  }
];

/**
 * Kết nối MongoDB và khởi tạo dữ liệu
 */
async function connectAndInit() {
  try {
    log.info('Đang kết nối MongoDB...');
    await mongoose.connect(mongoUri, mongoConfig.options);
    log.success('Kết nối MongoDB thành công');

    await initJobTypes();

  } catch (error) {
    log.error(`Lỗi kết nối MongoDB: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Tìm tất cả các Unit (đơn vị) có sẵn trong database
 */
async function findExistingUnits() {
  try {
    log.info('Đang tìm kiếm các đơn vị có sẵn...');

    const units = await UnitModel.find({ status: 1 }).select('_id name nameAlias');
    const unitMap = new Map();

    units.forEach(unit => {
      unitMap.set(unit.name, unit);
    });

    log.info(`Tìm thấy ${units.length} đơn vị trong database`);
    return unitMap;

  } catch (error) {
    log.error(`Lỗi khi tìm kiếm đơn vị: ${error.message}`);
    throw error;
  }
}

/**
 * Tạo JobType mới
 */
async function createJobType(jobTypeData, unitId) {
  try {
    const nameAlias = change_alias(jobTypeData.name);

    const jobType = new JobTypeModel({
      name: jobTypeData.name,
      nameAlias: nameAlias,
      description: jobTypeData.description,
      unit: unitId,
      status: 1, // Hoạt động
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    await jobType.save();
    return jobType;

  } catch (error) {
    log.error(`Lỗi khi tạo JobType "${jobTypeData.name}": ${error.message}`);
    throw error;
  }
}

/**
 * Kiểm tra JobType đã tồn tại chưa
 */
async function checkJobTypeExists(name, unitId) {
  try {
    const existingJobType = await JobTypeModel.findOne({
      name: name,
      unit: unitId,
      status: 1 // Chỉ kiểm tra JobType đang hoạt động
    });

    return existingJobType !== null;

  } catch (error) {
    log.error(`Lỗi khi kiểm tra JobType tồn tại: ${error.message}`);
    throw error;
  }
}

/**
 * Hàm chính khởi tạo JobTypes
 */
async function initJobTypes() {
  try {
    log.info('Bắt đầu khởi tạo dữ liệu JobType...');

    // Tìm các đơn vị có sẵn
    const unitMap = await findExistingUnits();

    let totalCreated = 0;
    let totalSkipped = 0;
    let totalErrors = 0;

    // Xử lý từng tổ công tác
    for (const unitData of jobTypesData) {
      log.info(`\nXử lý đơn vị: ${unitData.unitName}`);

      // Tìm đơn vị trong database
      const unit = unitMap.get(unitData.unitName);
      if (!unit) {
        log.warn(`Không tìm thấy đơn vị "${unitData.unitName}" trong database. Bỏ qua...`);
        continue;
      }

      log.info(`Tìm thấy đơn vị: ${unit.name} (ID: ${unit._id})`);

      // Tạo các JobType cho đơn vị này
      for (const jobTypeData of unitData.jobTypes) {
        try {
          // Kiểm tra JobType đã tồn tại chưa
          const exists = await checkJobTypeExists(jobTypeData.name, unit._id);

          if (exists) {
            log.warn(`  JobType "${jobTypeData.name}" đã tồn tại. Bỏ qua...`);
            totalSkipped++;
          } else {
            // Tạo JobType mới
            const newJobType = await createJobType(jobTypeData, unit._id);
            log.success(`  Tạo thành công JobType: "${newJobType.name}" (Alias: ${newJobType.nameAlias})`);
            totalCreated++;
          }

        } catch (error) {
          log.error(`  Lỗi khi xử lý JobType "${jobTypeData.name}": ${error.message}`);
          totalErrors++;
        }
      }
    }

    // Hiển thị thống kê cuối cùng
    await displayFinalStatistics(totalCreated, totalSkipped, totalErrors);

    log.success('Khởi tạo dữ liệu JobType hoàn thành!');
    process.exit(0);

  } catch (error) {
    log.error(`Lỗi trong quá trình khởi tạo dữ liệu: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Hiển thị thống kê cuối cùng
 */
async function displayFinalStatistics(created, skipped, errors) {
  try {
    log.info('\n=== THỐNG KÊ KẾT QUẢ ===');

    // Thống kê JobType
    const totalJobTypes = await JobTypeModel.countDocuments();
    log.info(`Tổng số JobType trong database: ${totalJobTypes}`);
    log.info(`JobType được tạo mới: ${created}`);
    log.info(`JobType đã tồn tại (bỏ qua): ${skipped}`);

    if (errors > 0) {
      log.warn(`Số lỗi xảy ra: ${errors}`);
    }

    // Thống kê theo đơn vị
    const jobTypesByUnit = await JobTypeModel.aggregate([
      {
        $lookup: {
          from: 'units',
          localField: 'unit',
          foreignField: '_id',
          as: 'unitInfo'
        }
      },
      {
        $unwind: '$unitInfo'
      },
      {
        $group: {
          _id: '$unitInfo.name',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      }
    ]);

    if (jobTypesByUnit.length > 0) {
      log.info('\nThống kê JobType theo đơn vị:');
      jobTypesByUnit.forEach(item => {
        log.info(`- ${item._id}: ${item.count} loại công việc`);
      });
    }

  } catch (error) {
    log.error(`Lỗi khi hiển thị thống kê: ${error.message}`);
  }
}

// Khởi chạy script
if (require.main === module) {
  connectAndInit();
}

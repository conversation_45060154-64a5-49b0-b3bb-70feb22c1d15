/**
 * Middleware validation cho các API báo cáo
 * Đảm bảo tính nhất quán và hợp lệ của dữ liệu báo cáo
 */

const _ = require('lodash');
const moment = require('moment');
const CONSTANTS = require('../const');

/**
 * Validation cho báo cáo nhanh
 */
const validateQuickReport = (req, res, next) => {
  const { jobTypeId, reportDate, quantity } = req.body;
  const errors = [];

  // Kiểm tra jobTypeId
  if (!jobTypeId || !_.isString(jobTypeId) || jobTypeId.trim() === '') {
    errors.push('Loại công việc không được để trống');
  }

  // Kiểm tra reportDate
  if (!reportDate || !moment(reportDate, 'YYYY-MM-DD', true).isValid()) {
    errors.push('<PERSON><PERSON><PERSON> báo cáo không hợp lệ (định dạng YYYY-MM-DD)');
  } else {
    // Kiểm tra ngày không được trong tương lai quá 1 ngày
    const reportMoment = moment(reportDate, 'YYYY-MM-DD');
    const tomorrow = moment().add(1, 'day');
    if (reportMoment.isAfter(tomorrow)) {
      errors.push('Ngày báo cáo không được vượt quá ngày mai');
    }

    // Kiểm tra ngày không được quá 30 ngày trong quá khứ
    const thirtyDaysAgo = moment().subtract(30, 'days');
    if (reportMoment.isBefore(thirtyDaysAgo)) {
      errors.push('Ngày báo cáo không được quá 30 ngày trong quá khứ');
    }
  }

  // Kiểm tra quantity
  if (quantity === undefined || quantity === null) {
    errors.push('Số lượng không được để trống');
  } else if (!Number.isInteger(quantity) || quantity < 0) {
    errors.push('Số lượng phải là số nguyên không âm');
  } else if (quantity > 1000) {
    errors.push('Số lượng không được vượt quá 1000');
  }

  // Kiểm tra note nếu có
  if (req.body.note && req.body.note.length > 500) {
    errors.push('Ghi chú không được vượt quá 500 ký tự');
  }

  if (errors.length > 0) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Thông báo',
        body: errors.join(', ')
      }
    });
  }

  next();
};

/**
 * Validation cho báo cáo chi tiết
 */
const validateDetailedReport = (req, res, next) => {
  const { 
    jobTypeId, 
    reportDate, 
    caseCode, 
    status, 
    description,
    completedTime,
    result
  } = req.body;
  const errors = [];

  // Kiểm tra jobTypeId
  if (!jobTypeId || !_.isString(jobTypeId) || jobTypeId.trim() === '') {
    errors.push('Loại công việc không được để trống');
  }

  // Kiểm tra reportDate
  if (!reportDate || !moment(reportDate, 'YYYY-MM-DD', true).isValid()) {
    errors.push('Ngày báo cáo không hợp lệ (định dạng YYYY-MM-DD)');
  } else {
    // Kiểm tra ngày không được trong tương lai quá 1 ngày
    const reportMoment = moment(reportDate, 'YYYY-MM-DD');
    const tomorrow = moment().add(1, 'day');
    if (reportMoment.isAfter(tomorrow)) {
      errors.push('Ngày báo cáo không được vượt quá ngày mai');
    }

    // Kiểm tra ngày không được quá 90 ngày trong quá khứ
    const ninetyDaysAgo = moment().subtract(90, 'days');
    if (reportMoment.isBefore(ninetyDaysAgo)) {
      errors.push('Ngày báo cáo không được quá 90 ngày trong quá khứ');
    }
  }

  // Kiểm tra caseCode
  if (!caseCode || !_.isString(caseCode) || caseCode.trim() === '') {
    errors.push('Mã số vụ việc không được để trống');
  } else {
    const trimmedCaseCode = caseCode.trim();
    if (trimmedCaseCode.length < 3 || trimmedCaseCode.length > 50) {
      errors.push('Mã số vụ việc phải từ 3-50 ký tự');
    }
    // Kiểm tra ký tự hợp lệ (chữ, số, dấu gạch ngang, gạch dưới)
    if (!/^[a-zA-Z0-9\-_]+$/.test(trimmedCaseCode)) {
      errors.push('Mã số vụ việc chỉ được chứa chữ cái, số, dấu gạch ngang và gạch dưới');
    }
  }

  // Kiểm tra status
  if (!status || !['processing', 'completed'].includes(status)) {
    errors.push('Trạng thái phải là "processing" hoặc "completed"');
  }

  // Kiểm tra description
  if (!description || !_.isString(description) || description.trim() === '') {
    errors.push('Mô tả vụ việc không được để trống');
  } else if (description.trim().length < 10) {
    errors.push('Mô tả vụ việc phải có ít nhất 10 ký tự');
  } else if (description.length > 1000) {
    errors.push('Mô tả vụ việc không được vượt quá 1000 ký tự');
  }

  // Kiểm tra logic cho trạng thái completed
  if (status === 'completed') {
    if (!completedTime) {
      errors.push('Thời gian hoàn thành là bắt buộc khi trạng thái là "completed"');
    } else if (!Number.isInteger(completedTime) || completedTime <= 0) {
      errors.push('Thời gian hoàn thành không hợp lệ');
    }

    if (!result || !_.isString(result) || result.trim() === '') {
      errors.push('Kết quả xử lý là bắt buộc khi trạng thái là "completed"');
    } else if (result.trim().length < 5) {
      errors.push('Kết quả xử lý phải có ít nhất 5 ký tự');
    } else if (result.length > 1000) {
      errors.push('Kết quả xử lý không được vượt quá 1000 ký tự');
    }
  }

  if (errors.length > 0) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Thông báo',
        body: errors.join(', ')
      }
    });
  }

  next();
};

/**
 * Validation cho cập nhật báo cáo
 */
const validateUpdateReport = (req, res, next) => {
  const { reportId } = req.body;
  const errors = [];

  // Kiểm tra reportId
  if (!reportId || !_.isString(reportId) || reportId.trim() === '') {
    errors.push('ID báo cáo không được để trống');
  }

  // Kiểm tra có ít nhất một trường để cập nhật
  const updateFields = ['quantity', 'note', 'status', 'description', 'completedTime', 'result'];
  const hasUpdateField = updateFields.some(field => req.body.hasOwnProperty(field));
  
  if (!hasUpdateField) {
    errors.push('Phải có ít nhất một trường để cập nhật');
  }

  // Validation cho các trường cụ thể nếu có
  if (req.body.hasOwnProperty('quantity')) {
    const { quantity } = req.body;
    if (!Number.isInteger(quantity) || quantity < 0 || quantity > 1000) {
      errors.push('Số lượng phải là số nguyên từ 0-1000');
    }
  }

  if (req.body.hasOwnProperty('note') && req.body.note && req.body.note.length > 500) {
    errors.push('Ghi chú không được vượt quá 500 ký tự');
  }

  if (req.body.hasOwnProperty('status') && !['processing', 'completed'].includes(req.body.status)) {
    errors.push('Trạng thái phải là "processing" hoặc "completed"');
  }

  if (req.body.hasOwnProperty('description')) {
    const { description } = req.body;
    if (!description || description.trim().length < 10 || description.length > 1000) {
      errors.push('Mô tả vụ việc phải từ 10-1000 ký tự');
    }
  }

  if (req.body.hasOwnProperty('result')) {
    const { result } = req.body;
    if (result && (result.trim().length < 5 || result.length > 1000)) {
      errors.push('Kết quả xử lý phải từ 5-1000 ký tự');
    }
  }

  if (errors.length > 0) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Thông báo',
        body: errors.join(', ')
      }
    });
  }

  next();
};

/**
 * Validation cho tham số phân trang
 */
const validatePagination = (req, res, next) => {
  const { page = 1, limit = 20 } = req.body;
  const errors = [];

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);

  if (isNaN(pageNum) || pageNum < 1) {
    errors.push('Trang phải là số nguyên dương');
  }

  if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
    errors.push('Số lượng items phải từ 1-100');
  }

  if (errors.length > 0) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Thông báo',
        body: errors.join(', ')
      }
    });
  }

  // Gán lại giá trị đã validate
  req.body.page = pageNum;
  req.body.limit = limitNum;

  next();
};

module.exports = {
  validateQuickReport,
  validateDetailedReport,
  validateUpdateReport,
  validatePagination
};

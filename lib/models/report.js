const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

const ReportSchema = new mongoose.Schema(
  {
    // Thông tin người báo cáo
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Loại công việc báo cáo
    jobType: {
      type: Schema.Types.ObjectId,
      ref: 'JobType',
      required: true
    },

    // Khu vực địa bàn
    area: {
      type: Schema.Types.ObjectId,
      ref: 'Area'
    },

    // Loại báo cáo: 'quick' (báo cáo nhanh) hoặc 'detailed' (báo cáo chi tiết theo trạng thái)
    reportType: {
      type: String,
      enum: ['quick', 'detailed'],
      required: true,
      default: 'quick'
    },

    // Ngày báo cáo (định dạng YYYY-MM-DD)
    reportDate: {
      type: String,
      required: true
    },

    // Dữ liệu báo cáo nhanh - chỉ số lượng
    quickReportData: {
      // Số lượng sự việc
      quantity: {
        type: Number,
        default: 0
      },
      // Ghi chú ngắn gọn
      note: {
        type: String,
        maxlength: 500
      }
    },

    // Dữ liệu báo cáo chi tiết - theo trạng thái từng vụ việc
    detailedReportData: {
      // Mã số vụ việc (do người dùng tự đặt hoặc hệ thống tạo)
      caseCode: {
        type: String
      },
      // Trạng thái xử lý: 'processing' (đang xử lý) hoặc 'completed' (đã xử lý)
      status: {
        type: String,
        enum: ['processing', 'completed']
      },
      // Mô tả ngắn gọn về vụ việc
      description: {
        type: String,
        maxlength: 1000
      },
      // Thời gian bắt đầu xử lý
      startTime: {
        type: Number
      },
      // Thời gian hoàn thành (nếu đã xử lý xong)
      completedTime: {
        type: Number
      },
      // Kết quả xử lý
      result: {
        type: String,
        maxlength: 1000
      }
    },

    // Trạng thái báo cáo: 'active' (hoạt động) hoặc 'inactive' (đã xóa)
    status: {
      type: Number,
      default: 1
    },

    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Index để tối ưu hóa truy vấn
ReportSchema.index({ user: 1, reportDate: -1 });
ReportSchema.index({ jobType: 1, reportDate: -1 });
ReportSchema.index({ area: 1, reportDate: -1 });
ReportSchema.index({ reportType: 1, reportDate: -1 });

module.exports = mongoConnections("master").model("Report", ReportSchema)

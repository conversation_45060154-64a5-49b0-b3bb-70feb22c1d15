const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const UserSchema = new mongoose.Schema(
 {
  username: {
    type: String, // tên đăng nhập VD: "Tên, số hiệu. <PERSON><PERSON> d<PERSON>, tên Hoàng Đức Anh + số hiệu gồm 06 số hdanh123456"
    required: true,
  },
  password: {
    type: String,  // mật khẩu đăng nhập
    required: true
  },
  name: {
    type: String, // tên đầy đủ của người dùng
    required: true,
  },
  idNumber: {
    type: String, // số hiệu cán bộ: 6 số
    required: true,
  },
  dob: {
    type: String, // ngày sinh, định dạng DD/MM/YYYY
  },
  gender: {
    type: String // giới tính, Male hoặc Female
  },
  address: {
    type: String // địa chỉ Nơi ở
  },
  phones: {
   type: Array // danh sách số điện tho<PERSON>i, có thể có nhiều số
  },
  email: {
    type: String // địa chỉ email
  },
  avatar: {
    type: String // đường dẫn đến ảnh đại diện
  },
  rank: {
    type: String // cấp bậc
  },
  units: [{
    type: Schema.Types.ObjectId, // Đơn vị trực thuộc: Phân cấp từ Công an Phường đến tổ công tác đơn vị trực thuộc
    ref: 'Unit'
  }],
  position: {
    type: Schema.Types.ObjectId, // Chức vụ của cán bộ trong đơn vị "- Trưởng Công an phường - Phó trưởng Công an phường - Tổ phó- Cán bộ"
    ref: 'Position'
  },
  areas: [{
    type: Schema.Types.ObjectId, // Khu vực địa bàn của cán bộ, có thể là nhiều khu vực
    ref: 'Area'
  }],
  jobTypes: [{
    type: Schema.Types.ObjectId, // Danh sách các công việc cán bộ báo cáo
    ref: 'JobType'
  }],
  educationLevel: {
    type: String, // Trình độ học vấn, có thể là: "Tiểu học", "Trung học cơ sở", "Trung học phổ thông", "Cao đẳng", "Đại học", "Sau đại học"
  },
  politicalTheoryLevel: {
    type: String, // Trình độ lý luận chính trị, có thể là:  "Sơ cấp", "Trung cấp", "Cao cấp",
  },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
  status: {
    type: Number,
    default: 1,
  },
  active: {
   type: Number,
   default: 0,
  },
  apps: {
    type: [String], // danh sách các ứng dụng mà người dùng có thể truy cập, ví dụ: ['ioc', 'cms']
    default: 'ioc',
    enum: ['ioc', 'cms']
  },
  block: {
    type: Number,
    default: 0,
  },
  permissions: [{
    type: Schema.Types.ObjectId,
    ref: 'Permission'
  }],
  groupPermissions: [{
    type: Schema.Types.ObjectId,
    ref: 'GroupPermission'
  }]
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("user", UserSchema)

const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema
const AreaSchema = new mongoose.Schema(
 {
  name: {
    type: String, // tên khu vực địa bàn
    required: true,
  },
  nameAlias: {
    type: String, // tên khu vực địa bàn được chuẩn hóa
  },
  description: {
    type: String, // mô tả khu vực địa bàn
  },
  boundaries: {
    type: Schema.Types.Mixed, // ranh giới khu vực địa bàn
  },
  groups: [{
    type: String, // danh sách các tổ dân phố khu vực địa bàn, ví dụ: ['Tổ 1', 'Tổ 2']
  }],
  routes: [{
    type: String, // danh sách các tuyến đường trong khu vực địa bàn, ví dụ: ['Đường A', 'Đường B']
  }],
  familyCount: {
    type: Number, // số lượng hộ gia đình trong khu vực địa bàn
    default: 0,
  },
  populationCount: {
    type: Number, // số lượng dân cư trong khu vực địa bàn
  },
  area: {
    type: Number, // diện tích khu vực địa bàn tính bằng mét vuông
  },
  status: {
    type: Number,
    default: 1,
  },
  createdAt: { type: Number, default: Date.now },
  updatedAt: { type: Number, default: Date.now },
 },
 { id: false, versionKey: false },
)

module.exports = mongoConnections("master").model("area", AreaSchema)

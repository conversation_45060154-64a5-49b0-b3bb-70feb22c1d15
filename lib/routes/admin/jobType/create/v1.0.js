const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { change_alias } = require('../../../../util/tool');

const JobTypeModel = require('../../../../models/jobType');
const UnitModel = require('../../../../models/unit');

module.exports = (req, res) => {
  const userId = req.user._id;
  const { name, description = '', unitId } = req.body;

  let jobTypeData;
  let unitInfo;

  const checkParams = (next) => {

    // Kiểm tra tên công việc
    if (!name || !_.isString(name) || name.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tên công việc không được để trống'
        }
      });
    }

    const trimmedName = name.trim();
    if (trimmedName.length < 3 || trimmedName.length > 100) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tên công việc phải từ 3-100 ký tự'
        }
      });
    }

    // Kiểm tra đơn vị
    if (!unitId || !_.isString(unitId) || unitId.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn đơn vị thực hiện công việc'
        }
      });
    }

    jobTypeData = {
      name: trimmedName,
      description: description.trim(),
      unitId: unitId.trim()
    };

    next();
  };

  const checkUnit = (next) => {
    UnitModel.findById(jobTypeData.unitId, (err, unit) => {
      if (err) {
        logger.logError('Lỗi khi tìm đơn vị:', err);
        return next(err);
      }

      if (!unit || unit.status !== 1) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Đơn vị không tồn tại. Vui lòng kiểm tra lại.'
          }
        });
      }

      unitInfo = unit;

      next();
    });
  };

  const checkDuplicateName = (next) => {
    JobTypeModel.findOne({
      name: jobTypeData.name,
      status: 1 // Chỉ kiểm tra công việc đang hoạt động
    }, (err, existingJobType) => {
      if (err) {
        logger.logError('Lỗi khi kiểm tra tên trùng lặp:', err);
        return next(err);
      }

      if (existingJobType) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Công việc "${jobTypeData.name}" đã tồn tại. Vui lòng kiểm tra lại.`
          }
        });
      }

      next();
    });
  };

  const createJobType = (next) => {
    // Tạo nameAlias tự động từ name
    const nameAlias = change_alias(jobTypeData.name);

    const newJobType = new JobTypeModel({
      name: jobTypeData.name,
      nameAlias: nameAlias,
      description: jobTypeData.description,
      unit: jobTypeData.unitId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    newJobType.save((err, savedJobType) => {
      if (err) {
        logger.logError('Lỗi khi tạo công việc:', err);
        return next(err);
      }

      // Populate thông tin unit để trả về
      JobTypeModel.findById(savedJobType._id)
        .populate('unit', 'name')
        .exec((err, populatedJobType) => {
          if (err) {
            logger.logError('Lỗi khi populate JobType:', err);
            return next(err);
          }

          logger.logInfo(`Tạo công việc thành công - Name: ${jobTypeData.name}, Đơn vị: ${unitInfo.name}, User: ${userId}`);

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Tạo công việc thành công'
            },
            data: populatedJobType
          });
        });
    });
  };

  // Thực thi các bước
  async.waterfall([
    checkParams,
    checkUnit,
    checkDuplicateName,
    createJobType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};

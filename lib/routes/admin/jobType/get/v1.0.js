const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

const JobTypeModel = require('../../../../models/jobType');

module.exports = (req, res) => {
  const { jobTypeId } = req.body;

  const checkParams = (next) => {
    if (!jobTypeId || !_.isString(jobTypeId) || jobTypeId.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID công việc'
        }
      });
    }

    next();
  };

  const findJobType = (next) => {
    JobTypeModel.findById(jobTypeId.trim())
      .populate('unit', 'name')
      .exec((err, foundJobType) => {
        if (err) {
          logger.logError('Lỗi khi tìm công việc:', err);
          return next(err);
        }

        if (!foundJobType) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy công việc'
            }
          });
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Lấy thông tin công việc thành công'
          },
          data: foundJobType
        });
      });
  };

  // Thực thi các bước
  async.waterfall([
    checkParams,
    findJobType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};

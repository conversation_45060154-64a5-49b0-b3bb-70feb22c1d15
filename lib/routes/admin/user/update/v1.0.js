const _ = require("lodash");
const async = require("async");
const ms = require("ms");
const config = require("config");
const util = require("util");
const rp = require("request-promise");
const Joi = require("joi");
Joi.objectId = require("joi-objectid")(Joi);
;
const User = require("../../../../models/user");
const PositionModel = require("../../../../models/position");
const CONSTANTS = require("../../../../const");
const MESSAGES = require("../../../../message");
const tool = require("../../../../util/tool");
const MailUtil = require("../../../../util/mail");
const validator = require("validator");
const redisConnection = require("../../../../connections/redis");


module.exports = (req, res) => {
  const {username, name, phones, email, gender, avatar, dob, apps, address, rank, position, educationLevel, politicalTheoryLevel, idNumber} = req.body || ''
  const { units } = req.body || [];
  const _id = req.body._id || "";
  let permissions = [];
  let permissionInGroup = [];
  let groupPermissions = [];
  const userId = _.get(req,'user.id', '')
  let objUpdate = {};
  let updatedData = {};
  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    if (!username || (username && !username.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_USERNAME,
      });
    }
    if (!email || (email && !email.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_EMAIL,
      });
    }
    if (!validator.isEmail(email)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.EMAIL_NOT_VALID,
      });
    }
    if(!idNumber || (idNumber && !idNumber.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_IDNUMBER
      })
    }
    if (!name || (name && !name.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_NAME,
      });
    }
    if(!phones.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_PHONE
      })
    }
    if(!phones.every(phone => validator.isMobilePhone(phone, ['vi-VN']))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.PHONE_NOT_VALID
      })
    }
    if (!gender) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_GENDER,
      });
    }
    if (!["male", "female"].includes(gender)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_GENDER,
      });
    }

    if (!apps || apps && !apps.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_APPS,
      });
    }
    if(!['cms','ioc'].includes(...apps)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.APPS_NOT_VALID
      })
    }
    if (!units || !units.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_UNIT,
      });
    }
    next(null);
  };

  const checkUserExists = (next) => {
    User.find({
      _id: {
        $ne: _id,
      },
      $or:[
        {
          username
        },
        {
          email
        },
        {
          phones: {
            $in: phones
          }
        },
        {
          idNumber
        }
      ],
      status: 1,
    })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        if (results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.USER.EXISTS,
          });
        }
        next();
      });
  };

  const findOldUser = (next) => {
    User.findOne({
      _id,
    })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        oldUser = result;
        if(oldUser.position && oldUser.position.toString() !== position && userId === oldUser._id.toString()) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.USER.NOT_UPDATE_UNIT_YOURSELF,
          });
        }
        next();
      });
  };

  const findPermissions = (next) => {
     if (!position) {
      return next();
    }
    if (oldUser.position && oldUser.position.toString() === position) {
      return next();
    }
    PositionModel.find({
      _id: position,
    })
      .select("permissions groupPermissions")
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        results.forEach((pos) => {
          permissions = _.union(permissions, pos.permissions.map(String));
          groupPermissions = _.union(groupPermissions, pos.groupPermissions.map(String));
        });
        next();
      });
  };

  const findPermissionsInGroup = (next) => {
    GroupPermissionModel.find({
      _id: {
        $in: groupPermissions,
      },
    })
      .populate("permissions", "code -_id")
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        results.map((groupPermission) => {
          permissionInGroup = permissionInGroup.concat(groupPermission.permissions);
        });
        next();
      });
  }

  const updateUser = (next) => {
    objUpdate = {
      username,
      name,
      idNumber,
      dob,
      gender,
      address,
      email,
      phones,
      avatar,
      units,
      position,
      educationLevel,
      politicalTheoryLevel,
      apps,
      updatedAt: Date.now(),
    };
    if (permissions.length) {
      objUpdate.permissions = permissions;
    }
    if (groupPermissions.length) {
      objUpdate.groupPermissions = groupPermissions;
    }
    User.findOneAndUpdate(
      {
        _id,
      },
      objUpdate,
      {new: true}
    )
    .populate("permissions", "code _id")
    .lean()
    .exec((err, result) => {
      if (err) {
        return next(err);
      }
      updatedData = {
        ...result,
        permissions: result.permissions.map((permission) => permission._id),
      }
      result.permissions.forEach((permission) => {
        delete permission._id
      })
      if (permissions.length || groupPermissions.length) {
        redisConnection("master")
          .getConnection()
          .get(`user:${_id}`, (err, token) => {
            if (token) {
              const obj = {
                id: _id,
                permissions: [...result.permissions, ...permissionInGroup],
                groupPermissions,
              };
              redisConnection("master")
                .getConnection()
                .multi()
                .set(`user:${token}`, JSON.stringify(obj))
                .exec((err, result) => {});
            }
          });
      }
      next();
    });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.USER.UPDATE_SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'update_user',
        description: 'Cập nhật người dùng',
        data: objUpdate,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall(
    [checkParams, checkUserExists, findOldUser, findPermissions, findPermissionsInGroup, updateUser, writeLog],
    (err, data) => {
      err &&
        _.isError(err) &&
        (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
        });

      res.json(data || err);
    }
  );
};

const _ = require('lodash')
const async = require('async')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Area = require('../../../../models/area')
const tool = require('../../../../util/tool')


module.exports = (req, res) => {

  const name = req.body.name || '';
  const limit = req.body.limit || 10;
  const page = req.body.page || 1;

  const listAreas = (next) => {

    let objSearch = {
      status: 1
    }

    if (name && name.trim()) {
      const nameAlias = tool.change_alias(name.trim());

      // Tìm kiếm theo nameAlias (đã được chuẩn hóa)
      objSearch.nameAlias = { $regex: nameAlias };
    }

    Area
      .find(objSearch)
      .sort({ createdAt: 1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: results
        });
      })
  }

  async.waterfall([
    listAreas
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
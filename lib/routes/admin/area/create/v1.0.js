const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Area = require('../../../../models/area');
const tool = require('../../../../util/tool');


module.exports = (req, res) => {
  let {
    name = '',
    description = '',
    boundaries = [],
    groups = [],
    routes = [],
    familyCount = 0,
    populationCount = 0,
    area = 0,
  } = req.body;

  name = name.trim();
  description = description.trim();
  let updatedData = {};

  const checkParams = (next) => {
    if (!name) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.AREA.NOT_FOUND_NAME
      });
    }

    next();
  }

  const checkAreaExists = (next) => {
    Area
      .find({
        name
      })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        if (results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_EXISTS
          });
        }

        next();
      })
  }

  const createArea = (next) => {

    let objCreate = {
      name,
      nameAlias: tool.change_alias(name),
      routes,
      familyCount
    }

    if (description) {
      objCreate.description = description;
    }

    if (boundaries && boundaries.length) {
      objCreate.boundaries = boundaries;
    }

    if (groups && groups.length) {
      objCreate.groups = groups;
    }

    if (populationCount) {
      objCreate.populationCount = populationCount;
    }

    if (area) {
      objCreate.area = area;
    }

    Area
      .create(objCreate, (err, result) => {
        if (err) {
          return next(err);
        }

        updatedData = result;

        next();
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.AREA.CREATE_SUCCESS,
      data: updatedData
    });

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'create_area',
        description: 'Tạo khu vực địa bàn mới',
        data: req.body,
        updatedData,
      },
      () => { }
    );
  };


  async.waterfall([
    checkParams,
    checkAreaExists,
    createArea,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
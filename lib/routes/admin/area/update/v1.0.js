const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Area = require('../../../../models/area');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const idArea = req.body._id || '';
  let {
    name = '',
    description = '',
    boundaries = [],
    groups = [],
    routes = [],
    familyCount = 0,
    populationCount = 0,
    area = 0,
  } = req.body;
  name = name.trim();
  description = description.trim();

  let areaInf = {};
  let updatedData = {};

  const checkParams = (next) => {
    if (!idArea) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!name || !description || !routes || !familyCount || !populationCount || !area || !boundaries || !groups || !boundaries.length || !groups.length || !routes.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.AREA.NOT_CHANGE
      });
    }

    next();
  };

  const checkAreaExists = (next) => {
    Area.findById(idArea)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_NOT_EXISTS
          });
        }

        areaInf = result;

        next();
      });
  };

  const checkNameNotExists = (next) => {
    if (!name || name === areaInf.name) {
      return next();
    }

    Area.findOne({
      name,
      _id: {
        $ne: idArea,
      },
      status: 1,
    })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_EXISTS
          });
        }

        next();
      });
  };

  const updateArea = (next) => {
    let objUpdate = {};

    if (name && name !== areaInf.name) {
      objUpdate.name = name;
      objUpdate.nameAlias = tool.change_alias(name);
    }

    if (routes && routes.length && !_.isEqual(routes, areaInf.routes)) {
      objUpdate.routes = routes;
    }

    if (familyCount && familyCount !== areaInf.familyCount) {
      objUpdate.familyCount = familyCount;
    }

    if (description && description !== areaInf.description) {
      objUpdate.description = description;
    }

    if (boundaries && boundaries.length && !_.isEqual(boundaries, areaInf.boundaries)) {
      objUpdate.boundaries = boundaries;
    }

    if (groups && groups.length && !_.isEqual(groups, areaInf.groups)) {
      objUpdate.groups = groups;
    }

    if (populationCount && populationCount !== areaInf.populationCount) {
      objUpdate.populationCount = populationCount;
    }

    if (area && area !== areaInf.area) {
      objUpdate.area = area;
    }

    if (Object.keys(objUpdate).length) {
      objUpdate.updatedAt = Date.now();
    } else {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.AREA.NOT_CHANGE
      });
    }

    Area.findOneAndUpdate(
      {
        _id: idArea,
      },
      objUpdate,
      {
        new: true,
      }
    )
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        updatedData = result;

        next();
      });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.AREA.UPDATE_SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'update_area',
        description: 'Cập nhật khu vực địa bàn',
        data: req.body,
        updatedData,
      },
      () => { }
    );
  };

  async.waterfall([checkParams, checkAreaExists, checkNameNotExists, updateArea, writeLog], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};

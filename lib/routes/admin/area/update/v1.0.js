const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Area = require('../../../../models/area');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  const idArea = req.body._id || '';
  let {
    name = '',
    description = '',
    level = null, // cấp độ khu vực: 1 = khu vực lớn, 2 = tổ dân phố
    parent = null, // ID khu vực cha (chỉ áp dụng cho level 2)
    boundaries = [],
    groups = [],
    routes = [],
    familyCount = 0,
    populationCount = 0,
    area = 0,
  } = req.body;
  name = name.trim();
  description = description.trim();

  let areaInf = {};
  let updatedData = {};

  const checkParams = (next) => {
    if (!idArea) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!name) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.AREA.NOT_FOUND_NAME
      });
    }

    // Validation cho cấu trúc phân cấp (nếu có thay đổi level)
    if (level && ![1, 2].includes(level)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Cấp độ khu vực không hợp lệ. Chỉ chấp nhận 1 (khu vực lớn) hoặc 2 (tổ dân phố)'
      });
    }

    // Nếu là tổ dân phố (level 2) thì phải có parent
    if (level === 2 && !parent) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Tổ dân phố phải thuộc về một khu vực lớn'
      });
    }

    // Nếu là khu vực lớn (level 1) thì không được có parent
    if (level === 1 && parent) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Khu vực lớn không thể thuộc về khu vực khác'
      });
    }

    next();
  };

  const checkAreaExists = (next) => {
    Area.findById(idArea)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_NOT_EXISTS
          });
        }

        areaInf = result;

        next();
      });
  };

  const validateParentArea = (next) => {
    // Nếu không có parent hoặc parent không thay đổi thì bỏ qua bước này
    if (!parent || parent === areaInf.parent) {
      return next();
    }

    // Kiểm tra parent area có tồn tại và phải là level 1
    Area
      .findOne({
        _id: parent,
        status: 1,
        level: 1
      })
      .lean()
      .exec((err, parentArea) => {
        if (err) {
          return next(err);
        }

        if (!parentArea) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: 'Khu vực cha không tồn tại hoặc không phải là khu vực lớn'
          });
        }

        // Kiểm tra không được tự tham chiếu
        if (parent === idArea) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: 'Khu vực không thể tự tham chiếu chính nó'
          });
        }

        next();
      })
  };

  const checkNameNotExists = (next) => {
    if (!name || name === areaInf.name) {
      return next();
    }

    Area.findOne({
      name,
      _id: {
        $ne: idArea,
      },
      status: 1,
    })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.AREA.AREA_EXISTS
          });
        }

        next();
      });
  };

  const updateArea = (next) => {
    let objUpdate = {};

    if (name && name !== areaInf.name) {
      objUpdate.name = name;
      objUpdate.nameAlias = tool.change_alias(name);
    }

    // Cập nhật level nếu có thay đổi
    if (level && level !== areaInf.level) {
      objUpdate.level = level;
    }

    // Cập nhật parent và parentPath nếu có thay đổi
    if (parent !== undefined && parent !== areaInf.parent) {
      objUpdate.parent = parent;
      // Cập nhật parentPath: nếu có parent thì parentPath sẽ bao gồm parent
      objUpdate.parentPath = parent ? [parent] : [];
    }

    if (routes && routes.length && !_.isEqual(routes, areaInf.routes)) {
      objUpdate.routes = routes;
    }

    if (familyCount !== undefined && familyCount !== areaInf.familyCount) {
      objUpdate.familyCount = familyCount;
    }

    if (description && description !== areaInf.description) {
      objUpdate.description = description;
    }

    if (boundaries && boundaries.length && !_.isEqual(boundaries, areaInf.boundaries)) {
      objUpdate.boundaries = boundaries;
    }

    if (groups && groups.length && !_.isEqual(groups, areaInf.groups)) {
      objUpdate.groups = groups;
    }

    if (populationCount !== undefined && populationCount !== areaInf.populationCount) {
      objUpdate.populationCount = populationCount;
    }

    if (area && area !== areaInf.area) {
      objUpdate.area = area;
    }

    if (Object.keys(objUpdate).length) {
      objUpdate.updatedAt = Date.now();
    } else {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.AREA.NOT_CHANGE
      });
    }

    Area.findOneAndUpdate(
      {
        _id: idArea,
      },
      objUpdate,
      {
        new: true,
      }
    )
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        updatedData = result;

        next();
      });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.AREA.UPDATE_SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req, 'user.id', ''),
        action: 'update_area',
        description: 'Cập nhật khu vực địa bàn',
        data: req.body,
        updatedData,
      },
      () => { }
    );
  };

  async.waterfall([checkParams, checkAreaExists, validateParentArea, checkNameNotExists, updateArea, writeLog], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};

const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const User = require('../../../models/user')
const CONSTANTS = require('../../../const')
const MESSAGES = require('../../../message')
const GroupPermissionModel = require('../../../models/groupPermission')
const SystemLog = require('../../../models/systemLog')


module.exports = (req, res) => {
  const userId = req.user.id || '';
  const appName = _.get(req, 'body.appName', '');
  const platform = _.get(req, 'body.platform', 'web');
  let userInf;
  const checkParams = (next) => {
    if(!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    next();
  }

  const getUser = (next) => {
    User
      .findById(userId)
      .populate("permissions", "code active status -_id")
      .populate({
        path: "units",
        select: "name parentPath icon",
        populate: {
          path: "parentPath",
          select: "name",
        },
      })
      .populate("position", "name unit role")
      .select('-password')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          })
        }
        userInf = result;
        next()
      })
  }

  const findPermissionsInGroup = (next) => {
    GroupPermissionModel
      .find({
        _id:{
          $in: userInf.groupPermissions
        }
      })
      .populate('permissions', 'code -_id')
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        results.map((groupPermission) => {
          userInf.permissions = userInf.permissions.concat(groupPermission.permissions);
        })
        next();
      })
  }

  const trackUserAccess = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: userInf
    });

    SystemLog.create({
      user: userId,
      action: 'user_app_access',
      description: `User accessed app: ${appName}`,
      data: {
        platform: platform,
        appName: appName,
        userAgent: req.headers['user-agent'] || '',
        ip: req.headers.ip || req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] || '',
        device: req.headers.device ? JSON.parse(req.headers.device) : {}
      }
    }, (err) => {
      if (err) {
        console.error('Failed to log user access:', err);
      }
    });
  }


  async.waterfall([
    checkParams,
    getUser,
    findPermissionsInGroup,
    trackUserAccess
  ], (err, data) => {
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}
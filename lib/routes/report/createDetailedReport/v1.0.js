const _ = require('lodash');
const async = require('async');
const moment = require('moment');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const ReportModel = require('../../../models/report');
const JobTypeModel = require('../../../models/jobType');
const AreaModel = require('../../../models/area');

module.exports = (req, res) => {
  const userId = req.user._id;
  const userUnits = req.user.units || [];
  const userAreas = req.user.areas || [];
  
  let reportData;
  let jobTypeInfo;
  let areaInfo;

  const validateParams = (next) => {
    const { 
      jobTypeId, 
      areaId, 
      reportDate, 
      caseCode, 
      status, 
      description, 
      startTime, 
      completedTime, 
      result 
    } = req.body;

    // <PERSON><PERSON><PERSON> tra các tham số bắt buộc
    if (!jobTypeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn loại công việc báo cáo'
        }
      });
    }

    if (!reportDate) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn ngày báo cáo'
        }
      });
    }

    if (!caseCode || caseCode.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng nhập mã số vụ việc'
        }
      });
    }

    if (!status || !['processing', 'completed'].includes(status)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn trạng thái xử lý hợp lệ (processing/completed)'
        }
      });
    }

    if (!description || description.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng nhập mô tả vụ việc'
        }
      });
    }

    // Kiểm tra định dạng ngày
    if (!moment(reportDate, 'YYYY-MM-DD', true).isValid()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Định dạng ngày không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM-DD'
        }
      });
    }

    // Kiểm tra thời gian nếu trạng thái là completed
    if (status === 'completed') {
      if (!completedTime) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Vui lòng nhập thời gian hoàn thành cho vụ việc đã xử lý'
          }
        });
      }

      if (!result || result.trim() === '') {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Vui lòng nhập kết quả xử lý cho vụ việc đã hoàn thành'
          }
        });
      }
    }

    reportData = {
      jobTypeId,
      areaId,
      reportDate,
      caseCode: caseCode.trim(),
      status,
      description: description.trim(),
      startTime: startTime || Date.now(),
      completedTime: status === 'completed' ? (completedTime || Date.now()) : null,
      result: status === 'completed' ? (result || '').trim() : ''
    };

    next();
  };

  const checkJobType = (next) => {
    JobTypeModel.findById(reportData.jobTypeId)
      .populate('unit')
      .exec((err, jobType) => {
        if (err) {
          logger.logError('Lỗi khi tìm loại công việc:', err);
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }

        if (!jobType) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Loại công việc không tồn tại'
            }
          });
        }

        // Kiểm tra quyền báo cáo theo đơn vị
        const jobTypeUnitId = jobType.unit ? jobType.unit._id.toString() : null;
        const hasUnitPermission = !jobTypeUnitId || userUnits.some(unitId => unitId.toString() === jobTypeUnitId);

        if (!hasUnitPermission) {
          return next({
            code: CONSTANTS.CODE.ACCESS_DENINED,
            message: {
              head: 'Thông báo',
              body: 'Bạn không có quyền báo cáo cho loại công việc này'
            }
          });
        }

        jobTypeInfo = jobType;
        next();
      });
  };

  const checkArea = (next) => {
    if (!reportData.areaId) {
      return next();
    }

    AreaModel.findById(reportData.areaId, (err, area) => {
      if (err) {
        logger.logError('Lỗi khi tìm khu vực:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      if (!area) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Khu vực không tồn tại'
          }
        });
      }

      // Kiểm tra quyền báo cáo theo khu vực
      const hasAreaPermission = userAreas.some(areaId => areaId.toString() === area._id.toString());
      if (!hasAreaPermission) {
        return next({
          code: CONSTANTS.CODE.ACCESS_DENINED,
          message: {
            head: 'Thông báo',
            body: 'Bạn không có quyền báo cáo cho khu vực này'
          }
        });
      }

      areaInfo = area;
      next();
    });
  };

  const checkDuplicateCaseCode = (next) => {
    // Kiểm tra mã số vụ việc có bị trùng không
    const query = {
      user: userId,
      jobType: reportData.jobTypeId,
      'detailedReportData.caseCode': reportData.caseCode,
      reportType: 'detailed',
      status: 1
    };

    if (reportData.areaId) {
      query.area = reportData.areaId;
    }

    ReportModel.findOne(query, (err, existingReport) => {
      if (err) {
        logger.logError('Lỗi khi kiểm tra mã vụ việc trùng lặp:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      if (existingReport) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Mã số vụ việc "${reportData.caseCode}" đã tồn tại. Vui lòng sử dụng mã khác hoặc cập nhật báo cáo hiện có.`
          }
        });
      }

      next();
    });
  };

  const createReport = (next) => {
    const newReport = new ReportModel({
      user: userId,
      jobType: reportData.jobTypeId,
      area: reportData.areaId || null,
      reportType: 'detailed',
      reportDate: reportData.reportDate,
      detailedReportData: {
        caseCode: reportData.caseCode,
        status: reportData.status,
        description: reportData.description,
        startTime: reportData.startTime,
        completedTime: reportData.completedTime,
        result: reportData.result
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    newReport.save((err, savedReport) => {
      if (err) {
        logger.logError('Lỗi khi tạo báo cáo chi tiết:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      logger.logInfo(`Tạo báo cáo chi tiết thành công - User: ${userId}, JobType: ${reportData.jobTypeId}, CaseCode: ${reportData.caseCode}`);
      
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Tạo báo cáo chi tiết thành công'
        },
        data: {
          reportId: savedReport._id,
          reportDate: savedReport.reportDate,
          caseCode: savedReport.detailedReportData.caseCode,
          status: savedReport.detailedReportData.status,
          jobType: jobTypeInfo.name,
          area: areaInfo ? areaInfo.name : null
        }
      });
    });
  };

  // Thực thi các bước
  async.waterfall([
    validateParams,
    checkJobType,
    checkArea,
    checkDuplicateCaseCode,
    createReport
  ], (err, result) => {
    if (err) {
      return res.json(err);
    }
    res.json(result);
  });
};

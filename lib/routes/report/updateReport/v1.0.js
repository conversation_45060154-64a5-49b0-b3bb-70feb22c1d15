const _ = require('lodash');
const async = require('async');
const moment = require('moment');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const ReportModel = require('../../../models/report');

module.exports = (req, res) => {
  const userId = req.user._id;
  const userUnits = req.user.units || [];
  const userAreas = req.user.areas || [];
  
  let reportData;
  let existingReport;

  const validateParams = (next) => {
    const { reportId } = req.body;

    if (!reportId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID báo cáo cần cập nhật'
        }
      });
    }

    reportData = { reportId };
    next();
  };

  const findExistingReport = (next) => {
    ReportModel.findOne({
      _id: reportData.reportId,
      user: userId,
      status: 1
    })
    .populate('jobType')
    .populate('area')
    .exec((err, report) => {
      if (err) {
        logger.logError('Lỗi khi tìm báo cáo:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      if (!report) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Không tìm thấy báo cáo hoặc bạn không có quyền cập nhật báo cáo này'
          }
        });
      }

      existingReport = report;
      next();
    });
  };

  const validateUpdateData = (next) => {
    if (existingReport.reportType === 'quick') {
      // Cập nhật báo cáo nhanh
      const { quantity, note } = req.body;

      if (quantity !== undefined && (quantity < 0 || !Number.isInteger(quantity))) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Số lượng phải là số nguyên không âm'
          }
        });
      }

      reportData.updateData = {};
      if (quantity !== undefined) {
        reportData.updateData['quickReportData.quantity'] = quantity;
      }
      if (note !== undefined) {
        reportData.updateData['quickReportData.note'] = note;
      }

    } else if (existingReport.reportType === 'detailed') {
      // Cập nhật báo cáo chi tiết
      const { status, description, completedTime, result } = req.body;

      reportData.updateData = {};

      if (status && !['processing', 'completed'].includes(status)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Trạng thái không hợp lệ (processing/completed)'
          }
        });
      }

      if (status) {
        reportData.updateData['detailedReportData.status'] = status;
      }

      if (description !== undefined) {
        if (description.trim() === '') {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Mô tả vụ việc không được để trống'
            }
          });
        }
        reportData.updateData['detailedReportData.description'] = description.trim();
      }

      if (completedTime !== undefined) {
        reportData.updateData['detailedReportData.completedTime'] = completedTime;
      }

      if (result !== undefined) {
        reportData.updateData['detailedReportData.result'] = result.trim();
      }

      // Kiểm tra logic: nếu status = completed thì phải có completedTime và result
      const newStatus = status || existingReport.detailedReportData.status;
      if (newStatus === 'completed') {
        const newCompletedTime = completedTime !== undefined ? completedTime : existingReport.detailedReportData.completedTime;
        const newResult = result !== undefined ? result : existingReport.detailedReportData.result;

        if (!newCompletedTime) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Vụ việc đã hoàn thành phải có thời gian hoàn thành'
            }
          });
        }

        if (!newResult || newResult.trim() === '') {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Vụ việc đã hoàn thành phải có kết quả xử lý'
            }
          });
        }
      }
    }

    // Kiểm tra xem có dữ liệu nào để cập nhật không
    if (Object.keys(reportData.updateData).length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có dữ liệu nào để cập nhật'
        }
      });
    }

    next();
  };

  const updateReport = (next) => {
    reportData.updateData.updatedAt = Date.now();

    ReportModel.findByIdAndUpdate(
      reportData.reportId,
      { $set: reportData.updateData },
      { new: true }
    )
    .populate('jobType')
    .populate('area')
    .exec((err, updatedReport) => {
      if (err) {
        logger.logError('Lỗi khi cập nhật báo cáo:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      logger.logInfo(`Cập nhật báo cáo thành công - ReportId: ${reportData.reportId}, User: ${userId}`);

      let responseData = {
        reportId: updatedReport._id,
        reportType: updatedReport.reportType,
        reportDate: updatedReport.reportDate,
        jobType: updatedReport.jobType.name,
        area: updatedReport.area ? updatedReport.area.name : null,
        updatedAt: updatedReport.updatedAt
      };

      if (updatedReport.reportType === 'quick') {
        responseData.quantity = updatedReport.quickReportData.quantity;
        responseData.note = updatedReport.quickReportData.note;
      } else {
        responseData.caseCode = updatedReport.detailedReportData.caseCode;
        responseData.status = updatedReport.detailedReportData.status;
        responseData.description = updatedReport.detailedReportData.description;
        responseData.startTime = updatedReport.detailedReportData.startTime;
        responseData.completedTime = updatedReport.detailedReportData.completedTime;
        responseData.result = updatedReport.detailedReportData.result;
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật báo cáo thành công'
        },
        data: responseData
      });
    });
  };

  // Thực thi các bước
  async.waterfall([
    validateParams,
    findExistingReport,
    validateUpdateData,
    updateReport
  ], (err, result) => {
    if (err) {
      return res.json(err);
    }
    res.json(result);
  });
};

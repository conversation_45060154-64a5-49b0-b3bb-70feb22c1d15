const _ = require('lodash');
const async = require('async');
const moment = require('moment');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const ReportModel = require('../../../models/report');

module.exports = (req, res) => {
  const userId = req.user._id;
  const userUnits = req.user.units || [];
  const userAreas = req.user.areas || [];

  let queryData;
  let statistics;

  const validateParams = (next) => {
    const {
      startDate,
      endDate,
      jobTypeId,
      areaId,
      groupBy = 'date' // date, jobType, area, status
    } = req.body;

    // Kiểm tra định dạng ngày
    if (startDate && !moment(startDate, 'YYYY-MM-DD', true).isValid()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Định dạng ngày bắt đầu không hợp lệ (YYYY-MM-DD)'
        }
      });
    }

    if (endDate && !moment(endDate, 'YYYY-MM-DD', true).isValid()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Định dạng ngày kết thúc không hợp lệ (YYYY-MM-DD)'
        }
      });
    }

    // Kiểm tra groupBy
    if (!['date', 'jobType', 'area', 'status'].includes(groupBy)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tham số groupBy không hợp lệ (date/jobType/area/status)'
        }
      });
    }

    // Nếu không có startDate và endDate, mặc định lấy 30 ngày gần nhất
    const defaultStartDate = startDate || moment().subtract(30, 'days').format('YYYY-MM-DD');
    const defaultEndDate = endDate || moment().format('YYYY-MM-DD');

    queryData = {
      startDate: defaultStartDate,
      endDate: defaultEndDate,
      jobTypeId,
      areaId,
      groupBy
    };

    next();
  };

  const buildAggregationPipeline = (next) => {
    // Pipeline cơ bản
    const pipeline = [
      {
        $match: {
          user: userId,
          status: 1,
          reportDate: {
            $gte: queryData.startDate,
            $lte: queryData.endDate
          }
        }
      }
    ];

    // Thêm filter theo jobType nếu có
    if (queryData.jobTypeId) {
      pipeline[0].$match.jobType = queryData.jobTypeId;
    }

    // Thêm filter theo area nếu có
    if (queryData.areaId) {
      pipeline[0].$match.area = queryData.areaId;
    }

    // Thêm lookup để lấy thông tin jobType và area
    pipeline.push(
      {
        $lookup: {
          from: 'jobtypes',
          localField: 'jobType',
          foreignField: '_id',
          as: 'jobTypeInfo'
        }
      },
      {
        $lookup: {
          from: 'areas',
          localField: 'area',
          foreignField: '_id',
          as: 'areaInfo'
        }
      }
    );

    // Xây dựng group stage dựa trên groupBy
    let groupStage = {};

    switch (queryData.groupBy) {
      case 'date':
        groupStage = {
          $group: {
            _id: '$reportDate',
            totalReports: { $sum: 1 },
            quickReports: {
              $sum: {
                $cond: [{ $eq: ['$reportType', 'quick'] }, 1, 0]
              }
            },
            detailedReports: {
              $sum: {
                $cond: [{ $eq: ['$reportType', 'detailed'] }, 1, 0]
              }
            },
            totalQuantity: {
              $sum: {
                $cond: [
                  { $eq: ['$reportType', 'quick'] },
                  '$quickReportData.quantity',
                  0
                ]
              }
            },
            processingCases: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$reportType', 'detailed'] },
                      { $eq: ['$detailedReportData.status', 'processing'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            },
            completedCases: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$reportType', 'detailed'] },
                      { $eq: ['$detailedReportData.status', 'completed'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        };
        break;

      case 'jobType':
        groupStage = {
          $group: {
            _id: '$jobType',
            jobTypeName: { $first: { $arrayElemAt: ['$jobTypeInfo.name', 0] } },
            totalReports: { $sum: 1 },
            quickReports: {
              $sum: {
                $cond: [{ $eq: ['$reportType', 'quick'] }, 1, 0]
              }
            },
            detailedReports: {
              $sum: {
                $cond: [{ $eq: ['$reportType', 'detailed'] }, 1, 0]
              }
            },
            totalQuantity: {
              $sum: {
                $cond: [
                  { $eq: ['$reportType', 'quick'] },
                  '$quickReportData.quantity',
                  0
                ]
              }
            },
            processingCases: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$reportType', 'detailed'] },
                      { $eq: ['$detailedReportData.status', 'processing'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            },
            completedCases: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$reportType', 'detailed'] },
                      { $eq: ['$detailedReportData.status', 'completed'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        };
        break;

      case 'area':
        groupStage = {
          $group: {
            _id: '$area',
            areaName: { $first: { $arrayElemAt: ['$areaInfo.name', 0] } },
            totalReports: { $sum: 1 },
            quickReports: {
              $sum: {
                $cond: [{ $eq: ['$reportType', 'quick'] }, 1, 0]
              }
            },
            detailedReports: {
              $sum: {
                $cond: [{ $eq: ['$reportType', 'detailed'] }, 1, 0]
              }
            },
            totalQuantity: {
              $sum: {
                $cond: [
                  { $eq: ['$reportType', 'quick'] },
                  '$quickReportData.quantity',
                  0
                ]
              }
            },
            processingCases: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$reportType', 'detailed'] },
                      { $eq: ['$detailedReportData.status', 'processing'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            },
            completedCases: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$reportType', 'detailed'] },
                      { $eq: ['$detailedReportData.status', 'completed'] }
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        };
        break;

      case 'status':
        groupStage = {
          $group: {
            _id: {
              reportType: '$reportType',
              status: {
                $cond: [
                  { $eq: ['$reportType', 'detailed'] },
                  '$detailedReportData.status',
                  'quick'
                ]
              }
            },
            totalReports: { $sum: 1 },
            totalQuantity: {
              $sum: {
                $cond: [
                  { $eq: ['$reportType', 'quick'] },
                  '$quickReportData.quantity',
                  1
                ]
              }
            }
          }
        };
        break;
    }

    pipeline.push(groupStage);

    // Sắp xếp kết quả
    if (queryData.groupBy === 'date') {
      pipeline.push({ $sort: { _id: 1 } });
    } else {
      pipeline.push({ $sort: { totalReports: -1 } });
    }

    queryData.pipeline = pipeline;
    next();
  };

  const executeAggregation = (next) => {
    ReportModel.aggregate(queryData.pipeline, (err, results) => {
      if (err) {
        logger.logError('Lỗi khi thực hiện aggregation thống kê:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      statistics = results;
      next();
    });
  };

  const formatStatistics = (next) => {
    let formattedData = {};

    switch (queryData.groupBy) {
      case 'date':
        formattedData = {
          type: 'date',
          period: {
            startDate: queryData.startDate,
            endDate: queryData.endDate
          },
          data: statistics.map(item => ({
            date: item._id,
            totalReports: item.totalReports,
            quickReports: item.quickReports,
            detailedReports: item.detailedReports,
            totalQuantity: item.totalQuantity,
            processingCases: item.processingCases,
            completedCases: item.completedCases
          }))
        };
        break;

      case 'jobType':
        formattedData = {
          type: 'jobType',
          period: {
            startDate: queryData.startDate,
            endDate: queryData.endDate
          },
          data: statistics.map(item => ({
            jobTypeId: item._id,
            jobTypeName: item.jobTypeName || 'Không xác định',
            totalReports: item.totalReports,
            quickReports: item.quickReports,
            detailedReports: item.detailedReports,
            totalQuantity: item.totalQuantity,
            processingCases: item.processingCases,
            completedCases: item.completedCases
          }))
        };
        break;

      case 'area':
        formattedData = {
          type: 'area',
          period: {
            startDate: queryData.startDate,
            endDate: queryData.endDate
          },
          data: statistics.map(item => ({
            areaId: item._id,
            areaName: item.areaName || 'Không xác định',
            totalReports: item.totalReports,
            quickReports: item.quickReports,
            detailedReports: item.detailedReports,
            totalQuantity: item.totalQuantity,
            processingCases: item.processingCases,
            completedCases: item.completedCases
          }))
        };
        break;

      case 'status':
        formattedData = {
          type: 'status',
          period: {
            startDate: queryData.startDate,
            endDate: queryData.endDate
          },
          data: statistics.map(item => ({
            reportType: item._id.reportType,
            status: item._id.status,
            totalReports: item.totalReports,
            totalQuantity: item.totalQuantity
          }))
        };
        break;
    }

    // Tính tổng kết
    const summary = {
      totalReports: statistics.reduce((sum, item) => sum + (item.totalReports || 0), 0),
      totalQuickReports: statistics.reduce((sum, item) => sum + (item.quickReports || 0), 0),
      totalDetailedReports: statistics.reduce((sum, item) => sum + (item.detailedReports || 0), 0),
      totalQuantity: statistics.reduce((sum, item) => sum + (item.totalQuantity || 0), 0),
      totalProcessingCases: statistics.reduce((sum, item) => sum + (item.processingCases || 0), 0),
      totalCompletedCases: statistics.reduce((sum, item) => sum + (item.completedCases || 0), 0)
    };

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Lấy thống kê báo cáo thành công'
      },
      data: {
        statistics: formattedData,
        summary
      }
    });
  };

  // Thực thi các bước
  async.waterfall([
    validateParams,
    buildAggregationPipeline,
    executeAggregation,
    formatStatistics
  ], (err, result) => {
    if (err) {
      return res.json(err);
    }
    res.json(result);
  });
};

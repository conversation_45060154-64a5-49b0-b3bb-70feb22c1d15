const _ = require('lodash');
const async = require('async');
const moment = require('moment');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const ReportModel = require('../../../models/report');

module.exports = (req, res) => {
  const userId = req.user._id;
  const userUnits = req.user.units || [];
  const userAreas = req.user.areas || [];
  
  let queryData;
  let reports;
  let totalCount;

  const validateParams = (next) => {
    const { 
      reportType, 
      jobTypeId, 
      areaId, 
      startDate, 
      endDate, 
      status,
      page = 1, 
      limit = 20 
    } = req.body;

    // Kiểm tra định dạng ngày nếu có
    if (startDate && !moment(startDate, 'YYYY-MM-DD', true).isValid()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Định dạng ngày bắt đầu không hợp lệ (YYYY-MM-DD)'
        }
      });
    }

    if (endDate && !moment(endDate, 'YYYY-MM-DD', true).isValid()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Định dạng ngày kết thúc không hợp lệ (YYYY-MM-DD)'
        }
      });
    }

    // Kiểm tra reportType nếu có
    if (reportType && !['quick', 'detailed'].includes(reportType)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Loại báo cáo không hợp lệ (quick/detailed)'
        }
      });
    }

    // Kiểm tra status cho báo cáo chi tiết
    if (status && !['processing', 'completed'].includes(status)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Trạng thái không hợp lệ (processing/completed)'
        }
      });
    }

    // Kiểm tra page và limit
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);

    if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tham số phân trang không hợp lệ (page >= 1, limit 1-100)'
        }
      });
    }

    queryData = {
      reportType,
      jobTypeId,
      areaId,
      startDate,
      endDate,
      status,
      page: pageNum,
      limit: limitNum,
      skip: (pageNum - 1) * limitNum
    };

    next();
  };

  const buildQuery = (next) => {
    // Xây dựng query cơ bản
    const query = {
      user: userId,
      status: 1
    };

    // Lọc theo loại báo cáo
    if (queryData.reportType) {
      query.reportType = queryData.reportType;
    }

    // Lọc theo loại công việc
    if (queryData.jobTypeId) {
      query.jobType = queryData.jobTypeId;
    }

    // Lọc theo khu vực
    if (queryData.areaId) {
      query.area = queryData.areaId;
    }

    // Lọc theo khoảng thời gian
    if (queryData.startDate || queryData.endDate) {
      query.reportDate = {};
      if (queryData.startDate) {
        query.reportDate.$gte = queryData.startDate;
      }
      if (queryData.endDate) {
        query.reportDate.$lte = queryData.endDate;
      }
    }

    // Lọc theo trạng thái (chỉ áp dụng cho báo cáo chi tiết)
    if (queryData.status && queryData.reportType === 'detailed') {
      query['detailedReportData.status'] = queryData.status;
    }

    queryData.mongoQuery = query;
    next();
  };

  const getReports = (next) => {
    // Đếm tổng số báo cáo
    ReportModel.countDocuments(queryData.mongoQuery, (err, count) => {
      if (err) {
        logger.logError('Lỗi khi đếm báo cáo:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      totalCount = count;

      // Lấy danh sách báo cáo
      ReportModel.find(queryData.mongoQuery)
        .populate('jobType', 'name nameAlias')
        .populate('area', 'name nameAlias')
        .sort({ reportDate: -1, createdAt: -1 })
        .skip(queryData.skip)
        .limit(queryData.limit)
        .exec((err, reportList) => {
          if (err) {
            logger.logError('Lỗi khi lấy danh sách báo cáo:', err);
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }

          reports = reportList;
          next();
        });
    });
  };

  const formatResponse = (next) => {
    const formattedReports = reports.map(report => {
      const baseData = {
        reportId: report._id,
        reportType: report.reportType,
        reportDate: report.reportDate,
        jobType: {
          id: report.jobType._id,
          name: report.jobType.name,
          nameAlias: report.jobType.nameAlias
        },
        area: report.area ? {
          id: report.area._id,
          name: report.area.name,
          nameAlias: report.area.nameAlias
        } : null,
        createdAt: report.createdAt,
        updatedAt: report.updatedAt
      };

      if (report.reportType === 'quick') {
        return {
          ...baseData,
          quantity: report.quickReportData.quantity,
          note: report.quickReportData.note
        };
      } else {
        return {
          ...baseData,
          caseCode: report.detailedReportData.caseCode,
          status: report.detailedReportData.status,
          description: report.detailedReportData.description,
          startTime: report.detailedReportData.startTime,
          completedTime: report.detailedReportData.completedTime,
          result: report.detailedReportData.result
        };
      }
    });

    const pagination = {
      currentPage: queryData.page,
      totalPages: Math.ceil(totalCount / queryData.limit),
      totalItems: totalCount,
      itemsPerPage: queryData.limit,
      hasNextPage: queryData.page < Math.ceil(totalCount / queryData.limit),
      hasPrevPage: queryData.page > 1
    };

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Lấy danh sách báo cáo thành công'
      },
      data: {
        reports: formattedReports,
        pagination
      }
    });
  };

  // Thực thi các bước
  async.waterfall([
    validateParams,
    buildQuery,
    getReports,
    formatResponse
  ], (err, result) => {
    if (err) {
      return res.json(err);
    }
    res.json(result);
  });
};

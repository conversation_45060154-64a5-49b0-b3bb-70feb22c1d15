const _ = require('lodash');
const async = require('async');
const moment = require('moment');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const ReportModel = require('../../../models/report');
const JobTypeModel = require('../../../models/jobType');
const AreaModel = require('../../../models/area');

module.exports = (req, res) => {
  const userId = req.user._id;
  const userUnits = req.user.units || [];
  const userAreas = req.user.areas || [];
  
  let reportData;
  let jobTypeInfo;
  let areaInfo;

  const validateParams = (next) => {
    const { jobTypeId, areaId, reportDate, quantity, note } = req.body;

    // Kiểm tra các tham số bắt buộc
    if (!jobTypeId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn loại công việc báo cáo'
        }
      });
    }

    if (!reportDate) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn ngày báo cáo'
        }
      });
    }

    if (quantity === undefined || quantity === null || quantity < 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng nhập số lượng hợp lệ (>= 0)'
        }
      });
    }

    // Kiểm tra định dạng ngày
    if (!moment(reportDate, 'YYYY-MM-DD', true).isValid()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Định dạng ngày không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM-DD'
        }
      });
    }

    reportData = {
      jobTypeId,
      areaId,
      reportDate,
      quantity: parseInt(quantity),
      note: note || ''
    };

    next();
  };

  const checkJobType = (next) => {
    JobTypeModel.findById(reportData.jobTypeId)
      .populate('unit')
      .exec((err, jobType) => {
        if (err) {
          logger.logError('Lỗi khi tìm loại công việc:', err);
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }

        if (!jobType) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Loại công việc không tồn tại'
            }
          });
        }

        // Kiểm tra quyền báo cáo theo đơn vị
        const jobTypeUnitId = jobType.unit ? jobType.unit._id.toString() : null;
        const hasUnitPermission = !jobTypeUnitId || userUnits.some(unitId => unitId.toString() === jobTypeUnitId);

        if (!hasUnitPermission) {
          return next({
            code: CONSTANTS.CODE.ACCESS_DENINED,
            message: {
              head: 'Thông báo',
              body: 'Bạn không có quyền báo cáo cho loại công việc này'
            }
          });
        }

        jobTypeInfo = jobType;
        next();
      });
  };

  const checkArea = (next) => {
    if (!reportData.areaId) {
      return next();
    }

    AreaModel.findById(reportData.areaId, (err, area) => {
      if (err) {
        logger.logError('Lỗi khi tìm khu vực:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      if (!area) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Khu vực không tồn tại'
          }
        });
      }

      // Kiểm tra quyền báo cáo theo khu vực
      const hasAreaPermission = userAreas.some(areaId => areaId.toString() === area._id.toString());
      if (!hasAreaPermission) {
        return next({
          code: CONSTANTS.CODE.ACCESS_DENINED,
          message: {
            head: 'Thông báo',
            body: 'Bạn không có quyền báo cáo cho khu vực này'
          }
        });
      }

      areaInfo = area;
      next();
    });
  };

  const checkExistingReport = (next) => {
    // Kiểm tra xem đã có báo cáo nhanh cho ngày này chưa
    const query = {
      user: userId,
      jobType: reportData.jobTypeId,
      reportDate: reportData.reportDate,
      reportType: 'quick',
      status: 1
    };

    if (reportData.areaId) {
      query.area = reportData.areaId;
    }

    ReportModel.findOne(query, (err, existingReport) => {
      if (err) {
        logger.logError('Lỗi khi kiểm tra báo cáo tồn tại:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      if (existingReport) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Đã tồn tại báo cáo nhanh cho loại công việc này trong ngày. Vui lòng cập nhật báo cáo hiện có.'
          }
        });
      }

      next();
    });
  };

  const createReport = (next) => {
    const newReport = new ReportModel({
      user: userId,
      jobType: reportData.jobTypeId,
      area: reportData.areaId || null,
      reportType: 'quick',
      reportDate: reportData.reportDate,
      quickReportData: {
        quantity: reportData.quantity,
        note: reportData.note
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    newReport.save((err, savedReport) => {
      if (err) {
        logger.logError('Lỗi khi tạo báo cáo:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      logger.logInfo(`Tạo báo cáo nhanh thành công - User: ${userId}, JobType: ${reportData.jobTypeId}, Date: ${reportData.reportDate}`);
      
      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Tạo báo cáo nhanh thành công'
        },
        data: {
          reportId: savedReport._id,
          reportDate: savedReport.reportDate,
          quantity: savedReport.quickReportData.quantity,
          jobType: jobTypeInfo.name,
          area: areaInfo ? areaInfo.name : null
        }
      });
    });
  };

  // Thực thi các bước
  async.waterfall([
    validateParams,
    checkJobType,
    checkArea,
    checkExistingReport,
    createReport
  ], (err, result) => {
    if (err) {
      return res.json(err);
    }
    res.json(result);
  });
};

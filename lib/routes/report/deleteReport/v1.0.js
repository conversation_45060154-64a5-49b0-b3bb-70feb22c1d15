const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const ReportModel = require('../../../models/report');

module.exports = (req, res) => {
  const userId = req.user._id;
  
  let reportData;
  let existingReport;

  const validateParams = (next) => {
    const { reportId } = req.body;

    if (!reportId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID báo cáo cần xóa'
        }
      });
    }

    reportData = { reportId };
    next();
  };

  const findExistingReport = (next) => {
    ReportModel.findOne({
      _id: reportData.reportId,
      user: userId,
      status: 1
    })
    .populate('jobType', 'name')
    .populate('area', 'name')
    .exec((err, report) => {
      if (err) {
        logger.logError('Lỗi khi tìm báo cáo:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      if (!report) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Không tìm thấy báo cáo hoặc bạn không có quyền xóa báo cáo này'
          }
        });
      }

      existingReport = report;
      next();
    });
  };

  const deleteReport = (next) => {
    // Soft delete - chỉ cập nhật status = 0
    ReportModel.findByIdAndUpdate(
      reportData.reportId,
      { 
        $set: { 
          status: 0,
          updatedAt: Date.now()
        }
      },
      { new: true }
    )
    .exec((err, deletedReport) => {
      if (err) {
        logger.logError('Lỗi khi xóa báo cáo:', err);
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }

      logger.logInfo(`Xóa báo cáo thành công - ReportId: ${reportData.reportId}, User: ${userId}, Type: ${existingReport.reportType}`);

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Xóa báo cáo thành công'
        },
        data: {
          reportId: deletedReport._id,
          reportType: existingReport.reportType,
          reportDate: existingReport.reportDate,
          jobType: existingReport.jobType.name,
          area: existingReport.area ? existingReport.area.name : null,
          deletedAt: deletedReport.updatedAt
        }
      });
    });
  };

  // Thực thi các bước
  async.waterfall([
    validateParams,
    findExistingReport,
    deleteReport
  ], (err, result) => {
    if (err) {
      return res.json(err);
    }
    res.json(result);
  });
};
